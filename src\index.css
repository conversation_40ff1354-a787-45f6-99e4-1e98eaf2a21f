@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced color system with amber/gold and navy blue */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 220 69.4% 28.2%; /* Navy blue */
    --primary-foreground: 210 40% 98%;
    --secondary: 45 100% 60%; /* Gold */
    --secondary-foreground: 220 69.4% 28.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 45 100% 60%; /* Gold */
    --accent-foreground: 220 69.4% 28.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 45 100% 60%; /* Gold */
    --radius: 0.75rem;

    /* Custom brand colors - enhanced */
    --brand-gold: 45 100% 60%;
    --brand-gold-light: 45 100% 70%;
    --brand-navy: 220 69.4% 28.2%;
    --brand-navy-light: 220 64.4% 38.2%;
    --brand-cream: 45 50% 96%;
    --brand-sage: 120 15% 65%;
    --brand-gray: 220 20% 97%;
  }

  .dark {
    --background: 220 40% 13%;
    --foreground: 210 40% 98%;
    --card: 220 40% 13%;
    --card-foreground: 210 40% 98%;
    --popover: 220 40% 13%;
    --popover-foreground: 210 40% 98%;
    --primary: 45 100% 60%; /* Gold in dark mode */
    --primary-foreground: 220 40% 13%;
    --secondary: 220 69.4% 28.2%; /* Navy in dark mode */
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 45 100% 60%; /* Gold in dark mode */
    --accent-foreground: 220 40% 13%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 45 100% 60%; /* Gold in dark mode */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
      sans-serif;
    scroll-behavior: smooth;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
  }
}

/* Sophisticated animations */
@keyframes hero-fade-in {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes glow-elegant {
  0%,
  100% {
    box-shadow: 0 0 30px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 50px rgba(251, 191, 36, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes morph {
  0%,
  100% {
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  }
  50% {
    border-radius: 70% 30% 50% 50% / 60% 40% 40% 60%;
  }
}

/* Utility classes */
.animate-hero-fade-in {
  animation: hero-fade-in 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-up {
  animation: slide-up 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-glow-elegant {
  animation: glow-elegant 4s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-morph {
  animation: morph 8s ease-in-out infinite;
}

/* Enhanced gradients */
.gradient-hero {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-cream)) 0%,
    hsl(var(--brand-gold-light)) 25%,
    hsl(var(--brand-gold)) 50%,
    hsl(var(--brand-navy-light)) 75%,
    hsl(var(--brand-navy)) 100%
  );
}

.gradient-text-hero {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-navy)),
    hsl(var(--brand-gold)),
    hsl(var(--brand-navy))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 100%;
  animation: shimmer 3s linear infinite;
}

.gradient-button {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-gold)),
    hsl(var(--brand-gold-light))
  );
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gradient-button:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-gold-light)),
    hsl(var(--brand-gold))
  );
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(251, 191, 36, 0.3);
}

/* Sophisticated card styles */
.card-elegant {
  @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elegant:hover {
  @apply shadow-2xl border-gold-200/50;
  transform: translateY(-8px) scale(1.02);
}

.dark .card-elegant {
  @apply bg-gray-900/90 border-gray-800/50;
}

.dark .card-elegant:hover {
  @apply border-gold-400/30;
}

/* Morphing blob backgrounds */
.blob-bg {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-gold) / 0.3),
    hsl(var(--brand-navy) / 0.2)
  );
  filter: blur(40px);
}

/* Enhanced section styling */
.section-hero {
  background: linear-gradient(
    135deg,
    hsl(var(--brand-cream)) 0%,
    rgba(251, 191, 36, 0.1) 50%,
    hsl(var(--brand-cream)) 100%
  );
  position: relative;
  overflow: hidden;
}

.section-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(251, 191, 36, 0.1) 0%,
    transparent 70%
  );
  animation: morph 15s ease-in-out infinite;
}

/* Typography enhancements */
.text-hero {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

.text-elegant {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  line-height: 1.7;
  letter-spacing: 0.01em;
}

/* Refined hover states */
.hover-elegant {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-elegant:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Responsive typography and mobile dark mode fixes */
@media (max-width: 768px) {
  .text-hero {
    line-height: 1.1;
  }

  /* Mobile background image adjustment */
  .mobile-bg-adjust {
    background-position: center 9% !important;
    background-size: 100% 27% !important;
  }

  /* Ensure dark mode is properly applied on mobile */
  @media (prefers-color-scheme: dark) {
    html:not(.light) {
      color-scheme: dark;
    }
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    hsl(var(--brand-gold)),
    hsl(var(--brand-navy))
  );
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    hsl(var(--brand-gold-light)),
    hsl(var(--brand-navy-light))
  );
}
