import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import AboutUs from './pages/AboutUs';
import Programs from './pages/Programs';
import ContactPage from './pages/Contact';
import Products from './pages/Products';
import GalleryPage from './pages/GalleryPage';
import ImpactPage from './pages/Impact';
import Partners from './pages/Partners';
import NotFound from './pages/NotFound';
import { ThemeProvider } from './components/ThemeProvider';
import { CartProvider } from './contexts/CartContext';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme='dark' storageKey='wopede-theme'>
      <CartProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path='/' element={<Home />} />
              <Route path='/about' element={<AboutUs />} />
              <Route path='/programs' element={<Programs />} />
              <Route path='/contact' element={<ContactPage />} />
              <Route path='/products' element={<Products />} />
              <Route path='/gallery' element={<GalleryPage />} />
              <Route path='/impact' element={<ImpactPage />} />
              <Route path='/partners' element={<Partners />} />
              <Route path='*' element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </CartProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
