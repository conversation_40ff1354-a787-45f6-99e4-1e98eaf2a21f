import React from 'react';
import Layout from '../components/Layout';
import Gallery from '../components/Gallery';
import { Camera, Heart, Users, Award } from 'lucide-react';

const GalleryPage: React.FC = () => {
  const galleryStats = [
    { icon: Camera, label: 'Photos Captured', value: '500+', color: 'text-navy-600 dark:text-skyblue-400' },
    { icon: Heart, label: 'Stories Shared', value: '100+', color: 'text-skyblue-600 dark:text-navy-400' },
    { icon: Users, label: 'Women Featured', value: '250+', color: 'text-navy-700 dark:text-skyblue-300' },
    { icon: Award, label: 'Achievements', value: '50+', color: 'text-skyblue-700 dark:text-navy-300' },
  ];

  return (
    <Layout showGallery={false}>
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-navy-600 via-skyblue-600 to-navy-700 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/assets/bus1.jpg"
            alt="WOPEDE Gallery"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-navy-900/80 to-skyblue-900/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Our{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-skyblue-200 to-white">
              Gallery
            </span>
          </h1>
          <p className="text-xl text-skyblue-100 max-w-3xl mx-auto leading-relaxed">
            Capturing moments of transformation, empowerment, and hope. 
            Every image tells a story of resilience and growth.
          </p>
        </div>
      </section>

      {/* Gallery Stats */}
      <section className="py-16 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {galleryStats.map((stat, index) => (
              <div
                key={index}
                className="text-center p-6 bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-gradient-to-br from-navy-500 to-skyblue-500 rounded-full">
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-navy-900 dark:text-white mb-2">
                  {stat.value}
                </h3>
                <p className={`text-sm font-medium ${stat.color}`}>
                  {stat.label}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Gallery */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Moments of{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-navy-600 to-skyblue-600">
                Transformation
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through our collection of inspiring moments, showcasing the 
              incredible journey of women empowerment and community building.
            </p>
          </div>

          <Gallery />
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Gallery Categories
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Explore different aspects of our work and impact through organized collections.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Training Sessions',
                description: 'Women learning new skills and building their capabilities',
                image: '/assets/bus1.jpg',
                count: '150+ Photos',
              },
              {
                title: 'Community Events',
                description: 'Celebrations, workshops, and community gatherings',
                image: '/assets/bus2.jpg',
                count: '200+ Photos',
              },
              {
                title: 'Success Stories',
                description: 'Graduates and their achievements in business and life',
                image: '/assets/bus3.jpg',
                count: '100+ Photos',
              },
              {
                title: 'Behind the Scenes',
                description: 'Daily operations and the people who make it happen',
                image: '/assets/bus4.jpg',
                count: '75+ Photos',
              },
              {
                title: 'Products & Crafts',
                description: 'Beautiful handmade products created by our artisans',
                image: '/assets/bus1.jpg',
                count: '120+ Photos',
              },
              {
                title: 'Partnerships',
                description: 'Collaborations with organizations and community leaders',
                image: '/assets/bus2.jpg',
                count: '50+ Photos',
              },
            ].map((category, index) => (
              <div
                key={index}
                className="group bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
              >
                <div className="aspect-w-16 aspect-h-12 overflow-hidden">
                  <img
                    src={category.image}
                    alt={category.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-navy-900/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-navy-900 dark:text-white">
                      {category.title}
                    </h3>
                    <span className="text-sm text-skyblue-600 dark:text-skyblue-400 font-semibold bg-skyblue-100 dark:bg-skyblue-900/30 px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {category.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Share Your Story
          </h2>
          <p className="text-xl text-navy-100 mb-8 max-w-2xl mx-auto">
            Have a story to share or photos from our programs? We'd love to feature 
            your journey and inspire others in our community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Camera className="mr-2 h-5 w-5" />
              Submit Photos
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-navy-600 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              Share Your Story
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default GalleryPage;
