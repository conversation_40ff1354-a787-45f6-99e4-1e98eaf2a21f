import React from 'react';
import Layout from '../components/Layout';
import Hero from '../components/Hero';
import About from '../components/About';
import { ArrowRight, Heart, Users, Award } from 'lucide-react';

const Home: React.FC = () => {
  const stats = [
    { icon: Heart, label: 'Lives Impacted', value: '10,000+', color: 'text-navy-600 dark:text-skyblue-400' },
    { icon: Users, label: 'Women Empowered', value: '2,500+', color: 'text-skyblue-600 dark:text-navy-400' },
    { icon: Award, label: 'Years of Service', value: '25+', color: 'text-navy-700 dark:text-skyblue-300' },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <Hero />

      {/* Quick Stats Section */}
      <section className="py-16 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center p-8 bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-gradient-to-br from-navy-500 to-skyblue-500 rounded-full">
                    <stat.icon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold text-navy-900 dark:text-white mb-2">
                  {stat.value}
                </h3>
                <p className={`text-lg font-medium ${stat.color}`}>
                  {stat.label}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
                Empowering Women,{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-navy-600 to-skyblue-600">
                  Building Peace
                </span>
              </h2>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8 leading-relaxed">
                Since 1999, WOPEDE has been at the forefront of women's empowerment, 
                providing skills training, counseling, and community support to create 
                lasting change in our communities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="/about"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-navy-600 to-skyblue-600 hover:from-navy-700 hover:to-skyblue-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Learn More About Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </a>
                <a
                  href="/programs"
                  className="inline-flex items-center px-8 py-4 border-2 border-navy-600 dark:border-skyblue-400 text-navy-600 dark:text-skyblue-400 hover:bg-navy-600 hover:text-white dark:hover:bg-skyblue-400 dark:hover:text-navy-900 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
                >
                  View Our Programs
                </a>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-w-4 aspect-h-3 rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="/assets/bus1.jpg"
                  alt="Women empowerment"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-navy-900/30 to-transparent"></div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-gradient-to-br from-skyblue-400 to-navy-600 rounded-full opacity-20"></div>
              <div className="absolute -top-6 -left-6 w-24 h-24 bg-gradient-to-br from-navy-400 to-skyblue-600 rounded-full opacity-20"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl text-navy-100 mb-8 max-w-2xl mx-auto">
            Join us in our mission to empower women and build stronger communities. 
            Every contribution makes a lasting impact.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Get Involved
              <ArrowRight className="ml-2 h-5 w-5" />
            </a>
            <a
              href="/products"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-navy-600 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              Shop Our Products
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Home;
