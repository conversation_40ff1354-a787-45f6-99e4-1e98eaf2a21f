import React from 'react';
import {
  Mail,
  Phone,
  MapPin,
  Globe,
  Facebook,
  Instagram,
  Twitter,
} from 'lucide-react';
import Button from './Button';

const Contact = () => {
  return (
    <section
      id='contact'
      className='py-12 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      <div className='container mx-auto px-6'>
        <div className='max-w-6xl mx-auto'>
          {/* Enhanced section header */}
          <div className='text-center mb-10'>
            <h2 className='text-5xl md:text-4xl font-bold text-navy-800 dark:text-white mb-8 tracking-tight'>
              Contact Us
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-gold-400 to-gold-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-8 rounded-full'></div>
            <p className='text-xl text-navy-700 dark:text-white max-w-4xl mx-auto leading-relaxed font-light'>
              WOPEDE CBO welcomes inquiries and engagement from individuals,
              organizations, and communities who share our vision. Together, we
              can create a brighter future for women and their families.
            </p>
          </div>

          <div className='grid md:grid-cols-2 gap-2'>
            {/* Enhanced contact information */}
            <div className='space-y-5'>
              <div>
                <h3 className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                  Get in Touch
                </h3>
                <div className='space-y-5'>
                  <div className='flex items-start space-x-4 group'>
                    <div className='w-12 h-12 bg-gold-100 dark:bg-gold-700/30 rounded-xl flex items-center justify-center group-hover:bg-gold-500 dark:group-hover:bg-gold-600 transition-colors duration-300 shadow-md'>
                      <MapPin className='w-6 h-6 text-gold-600 dark:text-gold-400 group-hover:text-white' />
                    </div>
                    <div>
                      <span className='text-navy-800 dark:text-gold-300 font-semibold text-lg block mb-1'>
                        Address
                      </span>
                      <span className='text-navy-600 dark:text-gold-200/80 font-light'>
                        Kitengela, Kajiado, Kenya
                      </span>
                    </div>
                  </div>
                  <div className='flex items-start space-x-4 group'>
                    <div className='w-12 h-12 bg-gold-100 dark:bg-gold-900/30 rounded-xl flex items-center justify-center group-hover:bg-gold-600 dark:group-hover:bg-gold-600 transition-colors duration-300 shadow-md'>
                      <Mail className='w-6 h-6 text-gold-600 dark:text-gold-400 group-hover:text-white' />
                    </div>
                    <div>
                      <span className='text-navy-800 dark:text-gold-300 font-semibold text-lg block mb-1'>
                        Email
                      </span>
                      <a
                        href='mailto:<EMAIL>'
                        className='text-navy-600 dark:text-gold-200/80 hover:text-gold-600 dark:hover:text-gold-300 transition-colors duration-300 font-light'
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                  <div className='flex items-start space-x-4 group'>
                    <div className='w-12 h-12 bg-gold-100 dark:bg-gold-900/30 rounded-xl flex items-center justify-center group-hover:bg-gold-500 dark:group-hover:bg-gold-600 transition-colors duration-300 shadow-md'>
                      <Phone className='w-6 h-6 text-gold-600 dark:text-gold-400 group-hover:text-white' />
                    </div>
                    <div>
                      <span className='text-navy-800 dark:text-gold-300 font-semibold text-lg block mb-1'>
                        Phone
                      </span>
                      <div className='text-navy-600 dark:text-gold-200/80 font-light space-y-1'>
                        <div>+254 745 671 972</div>
                        <div>+254 796 558 054</div>
                        <div>+254 723 312 424</div>
                      </div>
                    </div>
                  </div>
                  <div className='flex items-start space-x-4 group'>
                    <div className='w-12 h-12 bg-gold-100 dark:bg-gold-900/30 rounded-xl flex items-center justify-center group-hover:bg-gold-500 dark:group-hover:bg-gold-600 transition-colors duration-300 shadow-md'>
                      <Globe className='w-6 h-6 text-gold-600 dark:text-gold-400 group-hover:text-white' />
                    </div>
                    <div>
                      <span className='text-navy-800 dark:text-gold-300 font-semibold text-lg block mb-1'>
                        Website
                      </span>
                      <a
                        href='http://www.wopedecbo.org'
                        className='text-navy-600 dark:text-gold-200/80 hover:text-gold-600 dark:hover:text-gold-300 transition-colors duration-300 font-light'
                      >
                        www.wopedecbo.org
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced social media section */}
              <div>
                <h3 className='text-3xl font-bold text-navy-800 dark:text-white mb-5'>
                  Follow Us
                </h3>
                <div className='space-y-4'>
                  <div className='flex items-center space-x-4 group'>
                    <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center group-hover:bg-blue-600 transition-colors duration-300 shadow-md'>
                      <Facebook className='w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:text-white' />
                    </div>
                    <span className='text-navy-600 dark:text-gold-200/80 font-light'>
                      Women for Peace and Development CBO
                    </span>
                  </div>
                  <div className='flex items-center space-x-4 group'>
                    <div className='w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-xl flex items-center justify-center group-hover:bg-pink-600 transition-colors duration-300 shadow-md'>
                      <Instagram className='w-6 h-6 text-pink-600 dark:text-pink-400 group-hover:text-white' />
                    </div>
                    <span className='text-navy-600 dark:text-gold-200/80 font-light'>
                      @wopede_cbo
                    </span>
                  </div>
                  <div className='flex items-center space-x-4 group'>
                    <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center group-hover:bg-blue-500 transition-colors duration-300 shadow-md'>
                      <Twitter className='w-6 h-6 text-blue-500 dark:text-blue-400 group-hover:text-white' />
                    </div>
                    <span className='text-navy-600 dark:text-gold-200/80 font-light'>
                      @WOPEDE_CBO
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced contact form */}
            <div className=' p-6 rounded-3xl shadow-2xl border border-gold-100 dark:border-gold-700/20 transform hover:-translate-y-1 transition-all duration-500 hover:shadow-gold-200/30 dark:hover:shadow-gold-700/20'>
              <h3 className='text-3xl font-bold text-navy-800 dark:text-white mb-8'>
                Send Us a Message
              </h3>
              <form className='space-y-4'>
                <div>
                  <label className='block text-navy-700 dark:text-gold-300 font-semibold mb-3 text-lg'>
                    Name
                  </label>
                  <input
                    type='text'
                    className='w-full px-6 py-4  border border-gold-200 dark:border-gold-700/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-gold-500 dark:focus:ring-gold-400 focus:border-transparent transition-all duration-300 font-light text-lg text-navy-800 dark:text-white'
                    placeholder='Your full name'
                  />
                </div>
                <div>
                  <label className='block text-navy-700 dark:text-gold-300 font-semibold mb-3 text-lg'>
                    Email
                  </label>
                  <input
                    type='email'
                    className='w-full px-6 py-4  border border-gold-200 dark:border-gold-700/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-gold-500 dark:focus:ring-gold-400 focus:border-transparent transition-all duration-300 font-light text-lg text-navy-800 dark:text-white'
                    placeholder='Your email address'
                  />
                </div>
                <div>
                  <label className='block text-navy-700 dark:text-gold-300 font-semibold mb-3 text-lg'>
                    Subject
                  </label>
                  <input
                    type='text'
                    className='w-full px-6 py-4 bg-white  border border-gold-200 dark:border-gold-700/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-gold-500 dark:focus:ring-gold-400 focus:border-transparent transition-all duration-300 font-light text-lg text-navy-800 dark:text-white'
                    placeholder='Message subject'
                  />
                </div>
                <div>
                  <label className='block text-navy-700 dark:text-gold-300 font-semibold mb-3 text-lg'>
                    Message
                  </label>
                  <textarea
                    rows={6}
                    className='w-full px-6 py-4 bg-white  border border-gold-200 dark:border-gold-700/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-gold-500 dark:focus:ring-gold-400 focus:border-transparent transition-all duration-300 font-light text-lg resize-none text-navy-800 dark:text-white'
                    placeholder='Your message'
                  ></textarea>
                </div>
                <Button type='submit' fullWidth={true}>
                  Send Message
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
