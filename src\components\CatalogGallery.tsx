import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { GalleryItem } from '../types';

interface GalleryProps {
  items: GalleryItem[];
  autoSlide?: boolean;
  slideInterval?: number;
}

const Gallery: React.FC<GalleryProps> = ({ 
  items, 
  autoSlide = false, 
  slideInterval = 3000 
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(autoSlide);

  useEffect(() => {
    if (!isAutoSliding) return;
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % items.length);
    }, slideInterval);
    
    return () => clearInterval(interval);
  }, [isAutoSliding, items.length, slideInterval]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % items.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + items.length) % items.length);
  };

  const toggleAutoSlide = () => {
    setIsAutoSliding(!isAutoSliding);
  };

  if (items.length === 0) return null;

  return (
    <div className='relative w-full'>
      {/* Header */}
      <div className='text-center mb-12'>
        <h2 className='text-4xl font-bold text-navy-900 dark:text-white mb-4'>
          Our Craft{' '}
          <span className='text-transparent bg-clip-text bg-gradient-to-r from-gold-500 to-gold-600'>
            Gallery
          </span>
        </h2>
        <p className='text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto'>
          Discover the beauty and craftsmanship behind our handmade products
        </p>
      </div>

      {/* Main Gallery */}
      <div className='relative bg-white/80 dark:bg-navy-800/80 backdrop-blur-md rounded-3xl shadow-2xl overflow-hidden border border-gray-200/50 dark:border-navy-700/50'>
        {/* Image Container */}
        <div className='relative h-96 md:h-[500px] overflow-hidden'>
          {items.map((item, index) => (
            <div
              key={item.id}
              className={`absolute inset-0 transition-all duration-700 ease-in-out ${
                index === currentSlide
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-105'
              }`}
            >
              <img
                src={item.image}
                alt={item.title}
                className='w-full h-full object-cover'
              />
              <div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent'></div>
            </div>
          ))}

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className='absolute left-4 top-1/2 -translate-y-1/2 p-3 bg-white/90 dark:bg-navy-800/90 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group'
          >
            <ChevronLeft className='h-6 w-6 text-navy-600 dark:text-gold-400 group-hover:text-gold-600 dark:group-hover:text-gold-300' />
          </button>
          
          <button
            onClick={nextSlide}
            className='absolute right-4 top-1/2 -translate-y-1/2 p-3 bg-white/90 dark:bg-navy-800/90 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group'
          >
            <ChevronRight className='h-6 w-6 text-navy-600 dark:text-gold-400 group-hover:text-gold-600 dark:group-hover:text-gold-300' />
          </button>

          {/* Auto-slide Toggle */}
          <button
            onClick={toggleAutoSlide}
            className='absolute top-4 right-4 p-3 bg-white/90 dark:bg-navy-800/90 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group'
          >
            {isAutoSliding ? (
              <Pause className='h-5 w-5 text-navy-600 dark:text-gold-400 group-hover:text-gold-600 dark:group-hover:text-gold-300' />
            ) : (
              <Play className='h-5 w-5 text-navy-600 dark:text-gold-400 group-hover:text-gold-600 dark:group-hover:text-gold-300' />
            )}
          </button>

          {/* Content Overlay */}
          <div className='absolute bottom-0 left-0 right-0 p-8'>
            <div className='max-w-2xl'>
              <span className='inline-block px-4 py-2 bg-gold-500/90 text-white rounded-full text-sm font-semibold mb-4'>
                {items[currentSlide].category}
              </span>
              <h3 className='text-3xl font-bold text-white mb-3'>
                {items[currentSlide].title}
              </h3>
              <p className='text-gold-100 text-lg leading-relaxed'>
                {items[currentSlide].description}
              </p>
            </div>
          </div>
        </div>

        {/* Thumbnail Navigation */}
        <div className='p-6 bg-gradient-to-r from-gray-50 to-white dark:from-navy-800 dark:to-navy-700'>
          <div className='flex gap-3 overflow-x-auto pb-2'>
            {items.map((item, index) => (
              <button
                key={item.id}
                onClick={() => setCurrentSlide(index)}
                className={`flex-shrink-0 w-20 h-20 rounded-xl overflow-hidden transition-all duration-300 ${
                  index === currentSlide
                    ? 'ring-4 ring-gold-500 shadow-lg scale-105'
                    : 'hover:scale-105 hover:shadow-md opacity-70 hover:opacity-100'
                }`}
              >
                <img
                  src={item.image}
                  alt={item.title}
                  className='w-full h-full object-cover'
                />
              </button>
            ))}
          </div>
        </div>

        {/* Slide Indicators */}
        <div className='absolute bottom-20 left-1/2 -translate-x-1/2 flex gap-2'>
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? 'bg-gold-500 scale-125'
                  : 'bg-white/50 hover:bg-white/80'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Gallery Stats */}
      <div className='mt-8 text-center'>
        <p className='text-gray-600 dark:text-gray-400'>
          <span className='font-semibold text-gold-600 dark:text-gold-400'>
            {currentSlide + 1}
          </span>{' '}
          of{' '}
          <span className='font-semibold text-navy-600 dark:text-white'>
            {items.length}
          </span>{' '}
          images
        </p>
      </div>
    </div>
  );
};

export default Gallery;
