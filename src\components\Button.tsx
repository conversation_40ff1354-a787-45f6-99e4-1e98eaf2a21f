import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  href?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  href, 
  onClick, 
  type = 'button',
  className = '',
  fullWidth = false
}) => {
  const baseClasses = 'inline-block bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-8 py-3 rounded-xl font-semibold text-lg hover:from-amber-700 hover:to-yellow-700 transition-all duration-500 transform hover:scale-105 shadow-lg';
  
  const widthClass = fullWidth ? 'w-full' : '';
  const allClasses = `${baseClasses} ${widthClass} ${className}`;

  if (href) {
    return (
      <a href={href} className={allClasses}>
        {children}
      </a>
    );
  }

  return (
    <button 
      type={type} 
      onClick={onClick} 
      className={allClasses}
    >
      {children}
    </button>
  );
};

export default Button;