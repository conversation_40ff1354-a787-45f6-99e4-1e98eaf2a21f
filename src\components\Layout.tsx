import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';
import Gallery from './Gallery';

interface LayoutProps {
  children: React.ReactNode;
  showGallery?: boolean;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  showGallery = true, 
  className = '' 
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-navy-50 via-skyblue-50/30 to-white dark:from-navy-900 dark:via-navy-800/50 dark:to-navy-950/30 ${className}`}>
      <Navbar />
      <main className="font-sans">
        {children}
      </main>
      {showGallery && (
        <section className="py-16">
          <div className="container mx-auto px-6">
            <Gallery />
          </div>
        </section>
      )}
      <Footer />
    </div>
  );
};

export default Layout;
