import React from 'react';
import Button from './Button';

const Programs = () => {
  const programs = [
    {
      title: 'Serenity Alliance',
      subtitle: 'Building Peace Together',
      description:
        'Promoting collaboration among women from diverse backgrounds, fostering unity and understanding through cultural exchange and peacebuilding initiatives.',
      features: [
        'Peace Building Capacity',
        'Cultural Exchange',
        'Community Solidarity',
      ],
      color: 'from-blue-500 to-blue-600',
      icon: '🕊️',
    },
    {
      title: 'Skills Development',
      subtitle: 'Empowering Through Education',
      description:
        'Equipping women with practical skills to foster economic independence and self-reliance through comprehensive training programs.',
      features: [
        'Hands-on Training',
        'Financial Literacy',
        'Entrepreneurship Support',
      ],
      image: '/assets/baskets.jpg',
      color: 'from-amber-500 to-amber-600',
      icon: '🎓',
    },
    {
      title: 'Community Health',
      subtitle: 'Healing Hearts & Minds',
      description:
        'Providing counseling services and trauma recovery programs for women facing emotional and psychological challenges.',
      features: ['Trauma Counseling', 'SGBV Support', 'Emotional Healing'],
      color: 'from-green-500 to-green-600',
      icon: '💚',
    },
  ];

  const trainings = [
    {
      name: 'Sewing & Knitting',
      icon: '🧵',
      description: 'Create beautiful clothing and accessories',
    },
    {
      name: 'Hairdressing & Beauty',
      icon: '💄',
      description: 'Professional grooming and styling skills',
    },
    {
      name: 'Catering & Baking',
      icon: '🍰',
      description: 'Culinary expertise for food businesses',
    },
    {
      name: 'Language Skills',
      icon: '🗣️',
      description: 'Communication and integration support',
    },
    {
      name: 'Digital Literacy',
      icon: '💻',
      description: 'Modern workplace computer skills',
    },
    {
      name: 'Financial Literacy',
      icon: '💰',
      description: 'Smart money management and budgeting',
    },
  ];

  return (
    <section
      id='programs'
      className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      {/* Elegant Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div className='absolute top-32 left-16 w-64 h-64 bg-gradient-to-r from-gold-300/10 to-gold-300/10 rounded-full blur-3xl animate-morph' />
        <div
          className='absolute bottom-32 right-16 w-80 h-80 bg-gradient-to-r from-navy-300/8 to-navy-400/8 rounded-full blur-3xl animate-morph'
          style={{ animationDelay: '4s' }}
        />
      </div>
      <div className='container mx-auto px-6 relative z-10'>
        <div className='max-w-6xl mx-auto'>
          {/* Main header */}
          <div className='text-center mb-16'>
            <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-8 tracking-tight gradient-text-hero'>
              Our Programs
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-8 rounded-full'></div>
            <p className='text-elegant text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
              Three powerful pillars designed to nurture women's potential and
              create lasting change in communities.
            </p>
          </div>

          {/* Three Pillars Grid */}
          <div className='relative mb-24 overflow-hidden rounded-3xl'>
            {/* Overlay */}
            <div className='absolute inset-0 bg-gradient-to-r from-blue-500/20 via-amber-500/20 to-green-500/20'></div>

            {/* Content */}
            <div className='relative z-10 p-8'>
              <div className='grid md:grid-cols-3 gap-8'>
                {programs.map((program, index) => (
                  <div
                    key={index}
                    className='group   rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gold-700 dark:border-gold-700'
                  >
                    <div
                      className={`bg-gradient-to-r ${program.color} p-6 text-white text-center`}
                    >
                      <div className='text-4xl mb-3'>{program.icon}</div>

                      <h3 className='text-xl font-bold mb-1'>
                        {program.title}
                      </h3>
                      <p className='text-sm opacity-90'>{program.subtitle}</p>
                    </div>

                    <div className='p-8'>
                      <p className='text-navy-700 dark:text-gold-200/90 text-base leading-relaxed mb-6'>
                        {program.description}
                      </p>

                      <div className='space-y-2'>
                        {program.features.map((feature, idx) => (
                          <div key={idx} className='flex items-center text-sm'>
                            <div
                              className={`w-2 h-2 bg-gradient-to-r ${program.color} rounded-full mr-3`}
                            ></div>
                            <span className='text-navy-700 dark:text-gold-200/80'>
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Training Programs */}
          <div className='mt-15 mb-18'>
            <div className='text-center mb-12'>
              <h3 className='text-3xl font-bold text-navy-800 dark:text-white mb-4'>
                Training Programs
              </h3>
              <div className='w-16 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-4 rounded-full'></div>
              <p className='text-navy-700 dark:text-gold-200 text-lg max-w-2xl mx-auto'>
                Practical skills that transform lives and build sustainable
                futures.
              </p>
            </div>

            <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-y-8'>
              {trainings.map((training, index) => (
                <div
                  key={index}
                  className={`group p-6 text-center transition-all duration-300 ${
                    index < trainings.length - 1 && (index + 1) % 6 !== 0
                      ? 'border-r border-gold-700 dark:border-navy-600'
                      : ''
                  }`}
                >
                  <div className='text-3xl mb-4 group-hover:scale-110 transition-transform duration-300'>
                    {training.icon}
                  </div>
                  <h4 className='text-base font-semibold text-navy-800 dark:text-white mb-2'>
                    {training.name}
                  </h4>
                  <p className='text-sm text-navy-600 dark:text-white leading-relaxed'>
                    {training.description}
                  </p>
                </div>
              ))}
            </div>

            <div className='text-center mt-16'>
              <Button href='#contact'>Join Our Programs</Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Programs;
