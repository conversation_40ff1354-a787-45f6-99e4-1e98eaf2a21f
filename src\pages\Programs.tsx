import React from 'react';
import Layout from '../components/Layout';
import { BookOpen, Heart, Users, Briefcase, Home, Lightbulb } from 'lucide-react';

const Programs: React.FC = () => {
  const programs = [
    {
      icon: BookOpen,
      title: 'Skills Training',
      subtitle: 'Empowering Through Education',
      description: 'Comprehensive training programs in various vocational skills including tailoring, crafts, computer literacy, and business management.',
      features: [
        'Vocational Skills Development',
        'Computer & Digital Literacy',
        'Business Management Training',
        'Certification Programs',
      ],
      color: 'from-navy-500 to-skyblue-500',
      bgColor: 'from-navy-50 to-skyblue-50 dark:from-navy-700 dark:to-navy-600',
    },
    {
      icon: Heart,
      title: 'Counseling Services',
      subtitle: 'Healing and Support',
      description: 'Professional counseling and psychological support services to help women overcome trauma and build resilience.',
      features: [
        'Individual Counseling',
        'Group Therapy Sessions',
        'Trauma Recovery Programs',
        'Mental Health Support',
      ],
      color: 'from-skyblue-500 to-navy-500',
      bgColor: 'from-skyblue-50 to-navy-50 dark:from-navy-600 dark:to-navy-700',
    },
    {
      icon: Users,
      title: 'Community Support',
      subtitle: 'Building Stronger Networks',
      description: 'Creating supportive communities where women can connect, share experiences, and support each other\'s growth.',
      features: [
        'Support Groups',
        'Mentorship Programs',
        'Community Events',
        'Peer-to-Peer Networks',
      ],
      color: 'from-navy-600 to-skyblue-400',
      bgColor: 'from-navy-50 to-skyblue-50 dark:from-navy-700 dark:to-navy-600',
    },
    {
      icon: Briefcase,
      title: 'Economic Empowerment',
      subtitle: 'Financial Independence',
      description: 'Programs designed to help women achieve financial independence through entrepreneurship and employment opportunities.',
      features: [
        'Microfinance Support',
        'Business Incubation',
        'Job Placement Services',
        'Financial Literacy',
      ],
      color: 'from-skyblue-600 to-navy-400',
      bgColor: 'from-skyblue-50 to-navy-50 dark:from-navy-600 dark:to-navy-700',
    },
    {
      icon: Home,
      title: 'Family Support',
      subtitle: 'Strengthening Families',
      description: 'Comprehensive support for families, including childcare, family counseling, and parenting programs.',
      features: [
        'Childcare Services',
        'Parenting Workshops',
        'Family Counseling',
        'Educational Support',
      ],
      color: 'from-navy-500 to-skyblue-500',
      bgColor: 'from-navy-50 to-skyblue-50 dark:from-navy-700 dark:to-navy-600',
    },
    {
      icon: Lightbulb,
      title: 'Innovation Hub',
      subtitle: 'Creative Solutions',
      description: 'A space for innovation and creativity where women can develop new ideas, products, and solutions for community challenges.',
      features: [
        'Innovation Workshops',
        'Product Development',
        'Technology Training',
        'Creative Collaboration',
      ],
      color: 'from-skyblue-500 to-navy-500',
      bgColor: 'from-skyblue-50 to-navy-50 dark:from-navy-600 dark:to-navy-700',
    },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-navy-600 via-skyblue-600 to-navy-700 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/assets/bus1.jpg"
            alt="WOPEDE Programs"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-navy-900/80 to-skyblue-900/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Our{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-skyblue-200 to-white">
              Programs
            </span>
          </h1>
          <p className="text-xl text-skyblue-100 max-w-3xl mx-auto leading-relaxed">
            Comprehensive programs designed to empower women and strengthen communities 
            through education, support, and sustainable development.
          </p>
        </div>
      </section>

      {/* Programs Grid */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Transforming Lives Through Action
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Each program is carefully designed to address specific needs and create 
              lasting positive change in the lives of women and their families.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {programs.map((program, index) => (
              <div
                key={index}
                className={`bg-gradient-to-br ${program.bgColor} p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`}
              >
                <div className="flex items-start space-x-6">
                  <div className={`p-4 bg-gradient-to-br ${program.color} rounded-2xl flex-shrink-0`}>
                    <program.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="mb-4">
                      <h3 className="text-2xl font-bold text-navy-900 dark:text-white mb-2">
                        {program.title}
                      </h3>
                      <p className="text-lg font-semibold text-skyblue-600 dark:text-skyblue-400 mb-3">
                        {program.subtitle}
                      </p>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {program.description}
                      </p>
                    </div>
                    <div className="space-y-2">
                      {program.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center">
                          <div className="w-2 h-2 bg-gradient-to-r from-navy-500 to-skyblue-500 rounded-full mr-3"></div>
                          <span className="text-gray-700 dark:text-gray-300 font-medium">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Join Our Programs?
          </h2>
          <p className="text-xl text-navy-100 mb-8 max-w-2xl mx-auto">
            Take the first step towards empowerment and personal growth. 
            Our programs are designed to support you every step of the way.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Apply Now
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-navy-600 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              Learn More
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Programs;
