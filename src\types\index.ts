export interface Product {
  id: string;
  name: string;
  description: string;
  detailedDescription: string;
  image: string;
  images?: string[]; // Additional product images
  category: string;
  price: number;
  originalPrice?: number; // For showing discounts
  availability: 'in-stock' | 'limited' | 'out-of-stock';
  rating: number;
  reviews: number;
  colors: string[];
  sizes?: string[];
  features: string[];
  tags?: string[];
  isNew?: boolean;
  isFeatured?: boolean;
  discount?: number; // Percentage discount
}

export interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  productCount: number;
}

export interface CartItem {
  product: Product;
  quantity: number;
  selectedColor?: string;
  selectedSize?: string;
}

export interface FilterOptions {
  categories: string[];
  priceRange: [number, number];
  availability: string[];
  colors: string[];
  rating: number;
}

export interface SortOption {
  value: string;
  label: string;
}

export interface GalleryItem {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
}
