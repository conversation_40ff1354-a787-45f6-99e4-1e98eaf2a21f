import React, { useEffect, useState } from 'react';
import { Arrow<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Heart, Users, Award } from 'lucide-react';
import { useScrollTo } from '@/hooks/useScrollTo';
import AnimatedSection from './AnimatedSection';
import Button from './Button';

const Hero: React.FC = () => {
  const scrollTo = useScrollTo();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    {
      number: '25+',
      title: 'Years of Service',
      description: 'Empowering women since 1999',
      delay: 1800,
    },
    {
      number: '1000+',
      title: 'Lives Transformed',
      description: 'Women empowered with skills',
      delay: 2100,
    },
    {
      number: '6',
      title: 'Training Programs',
      description: 'Comprehensive skill development',
      delay: 2400,
    },
  ];

  return (
    <section
      className='min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-navy via-amber-50/30 to-white
      dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 mobile-bg-adjust'
      style={{
        backgroundImage: 'url("/assets/bg.png")',
        backgroundPosition: 'center 26%',
        backgroundSize: '50% 60%',
        backgroundRepeat: 'no-repeat',
        // backgroundBlendMode: 'soft-light',
      }}
    >
      <div className='absolute -top-10 -left-20 w-40 h-60 bg-navy dark:bg-gold-600 rounded-full blur-4xl'></div>
      <div className='absolute -bottom-20 -right-20 w-80 h-80 bg-navy-600 dark:bg-gold-600 rounded-full blur-3xl'></div>
      <div className='absolute top-10 right-10 w-60 h-60 bg-navy-400/30 dark:bg-gold-400/30 rounded-full blur-3xl'></div>
      {/* Dynamic Background Elements */}
      <div className='absolute inset-0 pointer-events-none bg-white/40 dark:bg-gray-900/60'>
        <div
          className='absolute w-80 h-80 bg-gradient-to-br from-gold-300/30 to-gold-500/20 dark:from-gold-500/20 dark:to-gold-600/10 animate-morph rounded-full blur-3xl'
          style={{
            top: '15%',
            left: '10%',
            transform: `translate(${mousePosition.x * 0.02}px, ${
              mousePosition.y * 0.02
            }px)`,
          }}
        />
        <div
          className='absolute w-96 h-96 bg-gradient-to-br from-navy-300/20 to-navy-600/10 dark:from-navy-400/20 dark:to-navy-500/10 animate-morph rounded-full blur-3xl'
          style={{
            top: '60%',
            right: '10%',
            animationDelay: '4s',
            transform: `translate(${mousePosition.x * -0.01}px, ${
              mousePosition.y * -0.01
            }px)`,
          }}
        />
        <div
          className='absolute w-72 h-72 bg-gradient-to-br from-gold-400/10 to-navy-400/10 dark:from-gold-500/10 dark:to-navy-500/10 animate-morph rounded-full blur-3xl'
          style={{
            bottom: '10%',
            left: '20%',
            animationDelay: '7s',
            transform: `translate(${mousePosition.x * 0.01}px, ${
              mousePosition.y * 0.01
            }px)`,
          }}
        />
      </div>
      {/* Main Content */}
      <div className='relative z-10 container mx-auto px-6'>
        <div className='max-w-6xl mx-auto text-center'>
          {/* Badge */}
          <AnimatedSection delay={0}>
            <div className='mb-6 sm:mb-8 mt-16 sm:mt-20'>
              <div className='inline-flex items-center space-x-2 sm:space-x-3 bg-navy-100/90 dark:bg-gold-200/90 backdrop-blur-md px-3 sm:px-6 py-2 sm:py-3 rounded-full border border-none dark:border-none shadow-xl hover:shadow-gold-200/50 dark:hover:shadow-gold-200/50 transition-all duration-300 max-w-full overflow-hidden'>
                <Sparkles className='w-4 h-4 text-gold dark:text-navy-900 animate-pulse flex-shrink-0' />
                <span className='text-navy-700 dark:text-navy-700 font-bold text-xs sm:text-sm whitespace-nowrap overflow-hidden text-ellipsis'>
                  Since 1999 • Transforming Lives in Kenya
                </span>
              </div>
            </div>
          </AnimatedSection>

          {/* Hero Title - Properly sized for desktop */}
          <div className='mb-8'>
            <AnimatedSection delay={300}>
              <h1 className='text-hero text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl mb-4 text-navy-900 dark:text-white'>
                <span className='block mb-2 animate-hero-fade-in'>
                  Empowering
                </span>
              </h1>
            </AnimatedSection>

            <AnimatedSection delay={600}>
              <h1 className='text-hero text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl mb-4'>
                <span className='block mb-2 bg-gradient-to-r from-gold-500 via-gold-400 to-gold-500 dark:from-gold-400 dark:via-gold-300 dark:to-gold-400 bg-clip-text text-transparent animate-hero-fade-in'>
                  Women
                </span>
              </h1>
            </AnimatedSection>

            <AnimatedSection delay={900}>
              <h1 className='text-hero text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl text-navy-900 dark:text-white'>
                <span className='block animate-hero-fade-in'>
                  Building Peace
                </span>
              </h1>
            </AnimatedSection>
          </div>

          {/* Subtitle */}
          <AnimatedSection delay={1200}>
            <div className='mb-8 max-w-3xl mx-auto'>
              <p className='text-elegant text-lg md:text-xl text-white dark:text-white mb-4 animate-slide-up'>
                Transforming lives through skills training, counseling and
                community support
              </p>
              <p className='text-elegant text-base text-navy  dark:text-white animate-slide-up'>
                Founded by Mme Cimpaye Modeste • Serving since 1999
              </p>
            </div>
          </AnimatedSection>

          {/* CTA Buttons */}
          <AnimatedSection delay={1500}>
            <div className='flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-12 sm:mb-16 px-2'>
              <button
                onClick={() => scrollTo('programs')}
                className='group bg-navy-800 dark:bg-transparent text-white px-4 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 border border-gold-700 dark:border-gold-600'
              >
                <div className='flex items-center justify-center space-x-2'>
                  <Heart className='w-5 h-5 group-hover:animate-pulse' />
                  <span>Our Story</span>
                </div>
              </button>

              <button
                onClick={() => scrollTo('business')}
                className='group bg-white dark:bg-navy-900 text-navy-800 dark:text-amber-300 px-4 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 dark:border-gold-700/30'
              >
                <div className='flex items-center justify-center space-x-2'>
                  <Award className='w-5 h-5 group-hover:animate-pulse text-gold-500 dark:text-gold-400' />
                  <span>Our Impact</span>
                </div>
              </button>

              <button
                onClick={() => scrollTo('contact')}
                className='group bg-navy-800 dark:bg-transparent text-white px-4 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 border border-gold-700 dark:border-gold-600'
              >
                <div className='flex items-center justify-center space-x-2'>
                  <Users className='w-5 h-5 group-hover:animate-pulse' />
                  <span>Join Our Mission</span>
                </div>
              </button>
            </div>
          </AnimatedSection>

          {/* Stats Grid */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-12'>
            {stats.map((stat, index) => (
              <AnimatedSection key={index} delay={stat.delay}>
                <div className='bg-white/80 dark:bg-navy-800/80 backdrop-blur-md p-8 rounded-2xl shadow-lg border border-gold-100 dark:border-gold-700/20 hover:-translate-y-2 hover:shadow-xl transition-all duration-300 group'>
                  <div className='text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gold-500 to-gold-400 dark:from-gold-400 dark:to-gold-300 bg-clip-text text-transparent mb-3 group-hover:animate-pulse'>
                    {stat.number}
                  </div>
                  <div className='text-lg font-bold text-navy-800 dark:text-white mb-2'>
                    {stat.title}
                  </div>
                  <div className='text-elegant text-sm text-navy-600 dark:text-gold-200/80'>
                    {stat.description}
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>

          {/* Scroll Indicator */}
          <AnimatedSection delay={2700}>
            <div className='text-center'>
              <button
                className='inline-flex flex-col items-center cursor-pointer group'
                onClick={() => scrollTo('programs')}
                aria-label='Scroll to programs section'
              >
                <p className='text-elegant text-navy-700 dark:text-gold-300 font-medium mb-3 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500 text-sm'>
                  Discover our programs
                </p>
                <div className='w-12 h-12 rounded-full border-2 border-gold-400 dark:border-gold-500 flex items-center justify-center group-hover:bg-gold-50 dark:group-hover:bg-gold-900/20 transition-all duration-500 shadow-md group-hover:shadow-lg'>
                  <ArrowDown
                    className='animate-bounce text-gold-500 dark:text-gold-400 group-hover:text-gold-600 dark:group-hover:text-gold-300 transition-colors duration-500'
                    size={20}
                  />
                </div>
              </button>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default React.memo(Hero);
