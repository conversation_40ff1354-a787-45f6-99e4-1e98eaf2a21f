import React from 'react';
import Layout from '../components/Layout';
import Impact from '../components/Impact';
import { TrendingUp, Users, Heart, Award, DollarSign, Home } from 'lucide-react';

const ImpactPage: React.FC = () => {
  const impactAreas = [
    {
      icon: Users,
      title: 'Women Empowered',
      description: 'Direct beneficiaries of our programs',
      stats: [
        { label: 'Skills Training Graduates', value: '2,500+' },
        { label: 'Counseling Recipients', value: '1,800+' },
        { label: 'Community Leaders Trained', value: '150+' },
      ],
      color: 'from-navy-500 to-skyblue-500',
    },
    {
      icon: DollarSign,
      title: 'Economic Impact',
      description: 'Financial empowerment and independence',
      stats: [
        { label: 'Businesses Started', value: '500+' },
        { label: 'Jobs Created', value: '1,200+' },
        { label: 'Income Increase Average', value: '300%' },
      ],
      color: 'from-skyblue-500 to-navy-500',
    },
    {
      icon: Home,
      title: 'Community Development',
      description: 'Strengthening families and communities',
      stats: [
        { label: 'Families Supported', value: '3,000+' },
        { label: 'Children Educated', value: '5,000+' },
        { label: 'Communities Reached', value: '25+' },
      ],
      color: 'from-navy-600 to-skyblue-400',
    },
  ];

  const testimonials = [
    {
      name: 'Sarah Wanjiku',
      role: 'Small Business Owner',
      image: '/assets/bus1.jpg',
      quote: 'WOPEDE gave me the skills and confidence to start my own tailoring business. Today, I employ 5 other women and support my family independently.',
    },
    {
      name: 'Grace Akinyi',
      role: 'Community Leader',
      image: '/assets/bus2.jpg',
      quote: 'The leadership training I received helped me become a voice for women in my community. We\'ve established support groups that continue to grow.',
    },
    {
      name: 'Mary Njeri',
      role: 'Program Graduate',
      image: '/assets/bus3.jpg',
      quote: 'After losing my husband, I felt lost. WOPEDE\'s counseling and skills training gave me hope and a new purpose in life.',
    },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-navy-600 via-skyblue-600 to-navy-700 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/assets/bus4.jpg"
            alt="WOPEDE Impact"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-navy-900/80 to-skyblue-900/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Our{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-skyblue-200 to-white">
              Impact
            </span>
          </h1>
          <p className="text-xl text-skyblue-100 max-w-3xl mx-auto leading-relaxed">
            Measuring success through the lives we've touched, the communities we've strengthened, 
            and the lasting change we've created together.
          </p>
        </div>
      </section>

      {/* Impact Statistics */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              25 Years of{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-navy-600 to-skyblue-600">
                Transformation
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Our impact spans across multiple dimensions of human development and community building.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {impactAreas.map((area, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex items-center mb-6">
                  <div className={`p-4 bg-gradient-to-br ${area.color} rounded-2xl mr-4`}>
                    <area.icon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-navy-900 dark:text-white">
                      {area.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {area.description}
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  {area.stats.map((stat, statIndex) => (
                    <div key={statIndex} className="flex justify-between items-center">
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        {stat.label}
                      </span>
                      <span className="text-2xl font-bold text-skyblue-600 dark:text-skyblue-400">
                        {stat.value}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Success Stories
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Real stories from real women whose lives have been transformed through our programs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h4 className="text-lg font-bold text-navy-900 dark:text-white">
                      {testimonial.name}
                    </h4>
                    <p className="text-skyblue-600 dark:text-skyblue-400 font-medium">
                      {testimonial.role}
                    </p>
                  </div>
                </div>
                <blockquote className="text-gray-700 dark:text-gray-300 italic leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Impact Component */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <Impact />
        </div>
      </section>

      {/* Future Goals */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">
              Looking Forward
            </h2>
            <p className="text-xl text-navy-100 max-w-2xl mx-auto">
              Our vision for the next decade of empowerment and community building.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { goal: 'Train 5,000 More Women', timeline: 'By 2030' },
              { goal: 'Establish 10 New Centers', timeline: 'Next 5 Years' },
              { goal: 'Create 2,000 Jobs', timeline: 'By 2028' },
              { goal: 'Reach 50 Communities', timeline: 'By 2035' },
            ].map((goal, index) => (
              <div key={index} className="text-center">
                <div className="bg-white/20 backdrop-blur-md rounded-2xl p-6 mb-4">
                  <TrendingUp className="h-12 w-12 text-white mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">
                    {goal.goal}
                  </h3>
                  <p className="text-navy-100">
                    {goal.timeline}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Join Our Mission
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ImpactPage;
