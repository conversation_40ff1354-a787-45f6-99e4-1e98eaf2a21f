import React from 'react';
import {
  Users,
  Award,
  Globe,
  Handshake,
  Target,
  Building,
  Heart,
} from 'lucide-react';
import Button from './Button';

const PartnershipsSection = () => {
  const partners = [
    {
      name: 'Pamoja Trust',
      description:
        'Supporting community development and empowerment initiatives across Kenya.',
      focus: 'Community Development',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
    },
    {
      name: 'Inkomoko',
      description:
        'Providing business training and financial services to entrepreneurs in East Africa.',
      focus: 'Business Training',
      icon: Target,
      color: 'from-green-500 to-green-600',
    },
    {
      name: 'Cohere',
      description:
        'Fostering social cohesion and integration in refugee and host communities.',
      focus: 'Social Cohesion',
      icon: Handshake,
      color: 'from-purple-500 to-purple-600',
    },
    {
      name: 'Refugee Point',
      description:
        'Connecting refugees with opportunities and resources for sustainable livelihoods.',
      focus: 'Refugee Support',
      icon: Globe,
      color: 'from-indigo-500 to-indigo-600',
    },
    {
      name: 'JRS (Jesuit Refugee Service)',
      description:
        'Serving, accompanying, and advocating for the rights of refugees and displaced persons.',
      focus: 'Refugee Services',
      icon: Award,
      color: 'from-amber-500 to-amber-600',
    },
  ];

  const opportunities = [
    {
      title: 'Volunteer Partnership',
      description:
        'Share your expertise and time to support our programs and initiatives.',
      icon: Heart,
      color: 'from-rose-500 to-pink-500',
      benefits: ['Skill sharing', 'Community impact', 'Personal growth'],
    },
    {
      title: 'Program Sponsorship',
      description:
        'Support specific training programs or sponsor individual participants.',
      icon: Award,
      color: 'from-emerald-500 to-teal-500',
      benefits: ['Direct impact', 'Progress tracking', 'Recognition'],
    },
    {
      title: 'Strategic Collaboration',
      description:
        'Partner with us to expand reach and amplify impact in communities.',
      icon: Building,
      color: 'from-violet-500 to-purple-500',
      benefits: ['Joint initiatives', 'Shared resources', 'Greater reach'],
    },
  ];

  return (
    <div className='bg-gradient-to-br from-brand-cream to-amber/400  rounded-3xl p-10 shadow-2xl border border-gold-100 dark:border-gold-700/20'>
      <div className='text-center mb-12'>
        <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-4 bg-gradient-to-r from-gold-500 via-gold-400 to-gold-500 dark:from-gold-400 dark:via-gold-300 dark:to-gold-400 bg-clip-text text-transparent'>
          Partnerships & Collaboration
        </h2>
        <div className='w-20 h-1 bg-gradient-to-r from-gold-400 to-gold-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-4 rounded-full'></div>
        <p className='text-lg text-navy-700 dark:text-white max-w-2xl mx-auto leading-relaxed'>
          Building stronger communities through strategic partnerships and
          meaningful collaboration opportunities.
        </p>
      </div>

      <div className='grid lg:grid-cols-2 gap-12 lg:divide-x lg:divide-gold-200 dark:lg:divide-gold-700/30'>
        {/* Our Partners Section */}
        <div className='space-y-6 p-6 bg-gradient-to-br from-white to-gold-50/30 dark:from-navy-800/20 dark:to-navy-700/10 rounded-2xl shadow-lg'>
          <div className='text-center mb-8'>
            <div className='flex items-center justify-center mb-4'>
              <Handshake className='w-6 h-6 text-gold-500 dark:text-gold-400 mr-3' />
              <h3 className='text-2xl font-bold text-navy-800 dark:text-white'>
                Our Partners
              </h3>
            </div>
            <p className='text-navy-700 dark:text-gold-200/80'>
              Trusted organizations working alongside us to create lasting
              change.
            </p>
          </div>

          <div className='space-y-4'>
            {partners.map((partner, index) => (
              <div
                key={index}
                className='group bg-gold/500  hover:shadow-lg transition-all duration-300 border border-navy-100 dark:border-gold-700/20 hover:border-gold-200 dark:hover:border-gold-600/30 rounded-xl p-5 hover:-translate-y-1'
              >
                <div className='flex items-start'>
                  <div
                    className={`w-10 h-10 bg-gradient-to-r ${partner.color} rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-md`}
                  >
                    <partner.icon className='w-5 h-5 text-white' />
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center mb-2'>
                      <h4 className='text-lg font-semibold text-navy-800 dark:text-white mr-3'>
                        {partner.name}
                      </h4>
                      <span className='text-xs text-navy-700 dark:text-gold-300 font-medium bg-gold-100 dark:bg-gold-900/30 px-3 py-1 rounded-full'>
                        {partner.focus}
                      </span>
                    </div>
                    <p className='text-navy-600 dark:text-white text-sm leading-relaxed'>
                      {partner.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className='text-center mt-6'>
            <p className='text-sm text-navy-700 dark:text-gold-200/80 mb-4'>
              Interested in becoming a partner?
            </p>
            <Button href='#contact' className='text-sm py-3'>
              Partner With Us
            </Button>
          </div>
        </div>

        {/* Partnership Opportunities Section */}
        <div className='space-y-6 p-6 bg-gradient-to-br from-white to-gold-50/30 dark:from-navy-800/20 dark:to-navy-700/10  rounded-2xl shadow-lg'>
          <div className='text-center mb-8'>
            <div className='flex items-center justify-center mb-4'>
              <Target className='w-6 h-6 text-gold-500 dark:text-gold-400 mr-3' />
              <h3 className='text-2xl font-bold text-navy-800 dark:text-white'>
                Partnership Opportunities
              </h3>
            </div>
            <p className='text-navy-700 dark:text-gold-200/80'>
              Multiple ways to join our mission and make a meaningful impact.
            </p>
          </div>

          <div className='space-y-4'>
            {opportunities.map((opportunity, index) => (
              <div
                key={index}
                className='group  hover:shadow-xl transition-all duration-300 border border-navy-100 dark:border-gold-700/20 hover:border-gold-200 dark:hover:border-gold-600/30 rounded-xl p-5 hover:-translate-y-1'
              >
                <div className='flex items-start mb-4'>
                  <div
                    className={`w-10 h-10 bg-gradient-to-r ${opportunity.color} rounded-lg flex items-center justify-center mr-4 shadow-md`}
                  >
                    <opportunity.icon className='w-5 h-5 text-white' />
                  </div>
                  <div className='flex-1'>
                    <h4 className='text-lg font-semibold text-navy-800 dark:text-white mb-2'>
                      {opportunity.title}
                    </h4>
                    <p className='text-navy-600 dark:text-white text-sm mb-3 leading-relaxed'>
                      {opportunity.description}
                    </p>
                    <div className='flex flex-wrap gap-2'>
                      {opportunity.benefits.map((benefit, idx) => (
                        <span
                          key={idx}
                          className='text-xs bg-gold-100 dark:bg-gold-900/30 text-navy-700 dark:text-gold-300 px-2 py-1 rounded-full'
                        >
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <a
                  href='#contact'
                  className='text-gold-600 dark:text-gold-400 font-medium text-sm hover:text-gold-700 dark:hover:text-gold-300 transition-colors'
                >
                  Learn More →
                </a>
              </div>
            ))}
          </div>

          <div className='text-center mt-6'>
            <Button href='#contact'>Explore Partnership</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export { PartnershipsSection };
