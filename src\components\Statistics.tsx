import React, { useState, useEffect } from 'react';

const Statistics = () => {
  const [counts, setCounts] = useState({
    years: 0,
    women: 0,
    programs: 0,
    communities: 0
  });

  const finalCounts = {
    years: 25,
    women: 1000,
    programs: 6,
    communities: 50
  };

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    const intervals = Object.keys(finalCounts).map(key => {
      const finalValue = finalCounts[key as keyof typeof finalCounts];
      const increment = finalValue / steps;
      let currentValue = 0;

      return setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
          currentValue = finalValue;
          clearInterval(intervals.find(i => i === interval));
        }
        setCounts(prev => ({
          ...prev,
          [key]: Math.floor(currentValue)
        }));
      }, stepDuration);
    });

    return () => intervals.forEach(clearInterval);
  }, []);

  const stats = [
    {
      number: counts.years,
      suffix: '+',
      label: 'Years of Service',
      description: 'Dedicated to women\'s empowerment',
      color: 'from-blue-500 to-blue-600'
    },
    {
      number: counts.women,
      suffix: '+',
      label: 'Women Empowered',
      description: 'Lives transformed through our programs',
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      number: counts.programs,
      suffix: '',
      label: 'Training Programs',
      description: 'Comprehensive skill development',
      color: 'from-green-500 to-green-600'
    },
    {
      number: counts.communities,
      suffix: '+',
      label: 'Communities Reached',
      description: 'Spreading peace and development',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Our Impact in Numbers</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Every number represents a life changed, a community strengthened, and hope restored.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2">
                <div className={`text-6xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {stat.number}{stat.suffix}
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {stat.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Statistics;