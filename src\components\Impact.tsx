import React from 'react';
import { <PERSON>, <PERSON>, Handshake, Quote, Award, Sparkles } from 'lucide-react';
import But<PERSON> from './Button';

const Impact = () => {
  const caseStudies = [
    {
      name: "<PERSON>'s Journey to Independence",
      initials: '<PERSON><PERSON>',
      story:
        "<PERSON>, a refugee from the Democratic Republic of Congo, joined WOPEDE's sewing program after losing her husband in the war. With no prior income, she struggled to provide for her children. After completing the training, <PERSON> began making school uniforms and selling them to local communities. Today, she runs a successful tailoring business and has become a mentor to other women in similar circumstances.",
      gradient: 'from-emerald-500 to-teal-600',
      program: 'Sewing Program',
      achievement: 'Successful Business Owner',
    },
    {
      name: "<PERSON>'s Courageous Comeback",
      initials: 'SC',
      story:
        "<PERSON>, a survivor of domestic violence, sought help through WOPEDE's counseling services. The trauma counseling sessions helped her regain confidence and rebuild her life. She later joined the catering program and now operates a small bakery, supporting her family and contributing to her community.",
      gradient: 'from-purple-500 to-pink-600',
      program: 'Catering Program',
      achievement: 'Bakery Owner',
    },
  ];

  return (
    <section
      id='impact'
      className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      <div className='container mx-auto px-6'>
        <div className='max-w-7xl mx-auto'>
          {/* Enhanced section header */}
          <div className='text-center mb-20'>
            <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-navy-800/80 backdrop-blur-md rounded-full px-8 py-4 shadow-lg mb-8 border border-gold-200 dark:border-gold-700/30'>
              <Sparkles className='w-5 h-5 text-gold-500 dark:text-gold-400' />
              <span className='text-navy-800 dark:text-gold-300 font-semibold text-sm tracking-wide'>
                Transforming Lives
              </span>
            </div>

            <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-8 tracking-tight gradient-text-hero'>
              Impact Stories
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-8 rounded-full'></div>
            <p className='text-elegant text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
              Real stories of transformation and empowerment from the women
              we've had the privilege to support.
            </p>
          </div>

          <div className=' grid md:grid-cols-2 gap-20  px-4 mb-5 relative  '>
            {/* Background decorative elements */}
            <div className='absolute -top-20 -left-20 w-40 h-64 bg-gold-600 dark:bg-navy-600 rounded-full blur-3xl'></div>
            <div className='absolute -bottom-20 -right-20 w-40 h-60  bg-navy-800 dark:bg-gold-600 rounded-full blur-3xl'></div>
            {caseStudies.map((study, index) => (
              <div
                key={index}
                className='group dark:bg-gray-900 rounded-xl overflow-hidden shadow-2xl transition-all duration-700 transform hover:-translate-y-2 relative z-10'
              >
                <div className='relative'>
                  {/* Avatar with initials */}
                  <div className='flex items-center justify-center p-10 '>
                    <div
                      className={`w-32 h-32 rounded-full bg-gradient-to-br ${study.gradient} flex items-center justify-center text-white text-3xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-500`}
                    >
                      {study.initials}
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className='absolute top-6 left-6'>
                    <div className='w-12 h-12 rounded-full bg-gold-100 dark:bg-gold-700/30 opacity-60 dark:opacity-40 animate-pulse'></div>
                  </div>
                  <div className='absolute bottom-6 right-6'>
                    <div
                      className='w-16 h-16 rounded-full bg-navy-100 dark:bg-navy-600/30 opacity-50 dark:opacity-30 animate-pulse'
                      style={{ animationDuration: '4s' }}
                    ></div>
                  </div>

                  {/* Program badge */}
                  <div className='absolute -bottom-4 left-1/2 transform -translate-x-1/2'>
                    <div className='aspect-[4/1] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 px-6 py-2 rounded-full shadow-md border border-gold-100 dark:border-gold-700/20'>
                      <span className='text-sm font-medium text-navy-700 dark:text-gold-300'>
                        {study.program}
                      </span>
                    </div>
                  </div>
                </div>

                <div className='p-10 pt-8'>
                  <div className='flex items-center justify-between mb-6'>
                    <h3 className='text-2xl font-bold text-navy-800 dark:text-white group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-300'>
                      {study.name}
                    </h3>
                    <span className='bg-gradient-to-r from-gold-100 to-gold-200 dark:from-gold-900/30 dark:to-gold-800/30 px-3 py-1 rounded-full text-xs font-medium text-navy-700 dark:text-gold-300'>
                      {study.achievement}
                    </span>
                  </div>

                  <div className='relative'>
                    <Quote className='absolute top-0 left-0 w-6 h-6 text-gold-300 dark:text-gold-700 transform -translate-x-2 -translate-y-2' />
                    <p className='text-navy-600 dark:text-white leading-relaxed text-lg font-light pl-4'>
                      {study.story}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className='text-center mt-16'>
            <p className='text-xl text-navy-700 dark:text-gold-200 mb-8 font-light'>
              Ready to be part of these inspiring stories?
            </p>
            <Button href='#contact'>
              <div className='flex items-center space-x-2'>
                <span>Join Our Mission</span>
                <span>→</span>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Impact;
