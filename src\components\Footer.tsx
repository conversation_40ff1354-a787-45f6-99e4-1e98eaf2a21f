import React from 'react';

const Footer = () => {
  return (
    <footer className='bg-gray-800 text-white py-12'>
      <div className='container mx-auto px-6'>
        <div className='max-w-6xl mx-auto'>
          <div className='grid md:grid-cols-3 gap-8 mb-8'>
            <div>
              <h3 className='text-2xl font-bold mb-4'>WOPEDE CBO</h3>
              <p className='text-gray-300 leading-relaxed mb-4'>
                Empowering women, transforming communities, and building peace
                since 1999. Together, we create lasting change through skills
                training, counseling, and community support.
              </p>
              <div className='text-sm text-gray-400'>
                <p>Kitengela, Kajiado, Kenya</p>
                <p>+254 745 671 972</p>
                <p><EMAIL></p>
              </div>
            </div>

            <div>
              <h4 className='text-lg font-semibold mb-4'>Our Programs</h4>
              <ul className='space-y-2 text-gray-300'>
                <li>Serenity Alliance (Peace)</li>
                <li>Skills Development</li>
                <li>Community Health Initiatives</li>
                <li>Trauma Counseling</li>
                <li>Financial Literacy</li>
                <li>Digital Literacy</li>
              </ul>
            </div>

            <div>
              <h4 className='text-lg font-semibold mb-4'>Get Involved</h4>
              <ul className='space-y-2 text-gray-300'>
                <li>Volunteer with Us</li>
                <li>Make a Donation</li>
                <li>Partner with WOPEDE</li>
                <li>Sponsor a Program</li>
                <li>Spread the Word</li>
              </ul>
            </div>
          </div>

          <div className='border-t border-gray-700 pt-8 text-center'>
            <p className='text-gray-300 mb-4'>
              <strong className='text-yellow-300'>
                "Empowering Women, Transforming Communities, Building Peace."
              </strong>
            </p>
            <p className='text-gray-400 text-sm'>
              © 2025 WOPEDE CBO. All rights reserved. | Women for Peace and
              Development Community-Based Organization
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
