import React from 'react';

const Footer = () => {
  return (
    <footer className='bg-gradient-to-br from-navy-800 to-navy-900 dark:from-navy-900 dark:to-navy-950 text-white py-12'>
      <div className='container mx-auto px-6'>
        <div className='max-w-6xl mx-auto'>
          <div className='grid md:grid-cols-3 gap-8 mb-8'>
            <div>
              <h3 className='text-2xl font-bold mb-4 text-skyblue-300'>
                WOPEDE CBO
              </h3>
              <p className='text-gray-300 leading-relaxed mb-4'>
                Empowering women, transforming communities, and building peace
                since 1999. Together, we create lasting change through skills
                training, counseling, and community support.
              </p>
              <div className='text-sm text-skyblue-200'>
                <p>Kitengela, Kajiado, Kenya</p>
                <p>+254 745 671 972</p>
                <p><EMAIL></p>
              </div>
            </div>

            <div>
              <h4 className='text-lg font-semibold mb-4 text-skyblue-300'>
                Our Programs
              </h4>
              <ul className='space-y-2 text-gray-300'>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Serenity Alliance (Peace)
                  </a>
                </li>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Skills Development
                  </a>
                </li>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Community Health Initiatives
                  </a>
                </li>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Trauma Counseling
                  </a>
                </li>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Financial Literacy
                  </a>
                </li>
                <li>
                  <a
                    href='/programs'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Digital Literacy
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className='text-lg font-semibold mb-4 text-skyblue-300'>
                Get Involved
              </h4>
              <ul className='space-y-2 text-gray-300'>
                <li>
                  <a
                    href='/contact'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Volunteer with Us
                  </a>
                </li>
                <li>
                  <a
                    href='/contact'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Make a Donation
                  </a>
                </li>
                <li>
                  <a
                    href='/partners'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Partner with WOPEDE
                  </a>
                </li>
                <li>
                  <a
                    href='/contact'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Sponsor a Program
                  </a>
                </li>
                <li>
                  <a
                    href='/gallery'
                    className='hover:text-skyblue-300 transition-colors'
                  >
                    Spread the Word
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className='border-t border-navy-700 pt-8 text-center'>
            <p className='text-gray-300 mb-4'>
              <strong className='text-skyblue-300'>
                "Empowering Women, Transforming Communities, Building Peace."
              </strong>
            </p>
            <p className='text-gray-400 text-sm'>
              © 2025 WOPEDE CBO. All rights reserved. | Women for Peace and
              Development Community-Based Organization
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
