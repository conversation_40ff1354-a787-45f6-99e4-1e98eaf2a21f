# Deployment Guide for WOPEDE Website

## Prerequisites
- Node.js (v16 or later)
- npm or yarn

## Build Steps

1. **Install dependencies**
   ```
   npm install
   ```
   or
   ```
   yarn
   ```

2. **Build the project**
   ```
   npm run build
   ```
   or
   ```
   yarn build
   ```

   This will create a `dist` folder with the production-ready files.

3. **Test the build locally**
   ```
   npm run preview
   ```
   or
   ```
   yarn preview
   ```

## Deployment Options

### Option 1: Static Hosting (Recommended)

You can deploy the contents of the `dist` folder to any static hosting service:

- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront
- Firebase Hosting

#### Important Notes for Static Hosting:

- Make sure all assets in the `public` folder are properly copied to the deployment server
- The `public` folder's contents will be available at the root path (e.g., `/assets/image.jpg`)
- If you're using a subdirectory for deployment, update the `base` option in `vite.config.ts`

### Option 2: Server Deployment

If deploying to a traditional server:

1. Upload the contents of the `dist` folder to your server's web root
2. Configure your server to handle client-side routing:

   **For Apache (.htaccess file):**
   ```
   <IfModule mod_rewrite.c>
     RewriteEngine On
     RewriteBase /
     RewriteRule ^index\.html$ - [L]
     RewriteCond %{REQUEST_FILENAME} !-f
     RewriteCond %{REQUEST_FILENAME} !-d
     RewriteRule . /index.html [L]
   </IfModule>
   ```

   **For Nginx:**
   ```
   location / {
     try_files $uri $uri/ /index.html;
   }
   ```

## Troubleshooting

### Images Not Displaying

If images are not displaying after deployment:

1. Check that all image paths start with a forward slash (`/`) and do NOT include `public/`
   - Correct: `/assets/image.jpg`
   - Incorrect: `public/assets/image.jpg`

2. Verify that the images were properly copied to the deployment server

3. Check browser console for 404 errors and verify the paths

4. If using a CDN or custom domain, ensure the base URL is correctly configured

### Routing Issues

If pages are not loading correctly:

1. Ensure your server is configured to handle client-side routing (see server configuration above)
2. Check that the `base` path in `vite.config.ts` matches your deployment subdirectory (if any)