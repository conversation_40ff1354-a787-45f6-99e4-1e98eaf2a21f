import React, { useState } from 'react';
import { Mail, CheckCircle, Loader2 } from 'lucide-react';
import Button from './Button';
const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail('');
    }, 1500);
  };

  if (isSubscribed) {
    return (
      <section className='py-16 bg-gradient-to-r from-navy-800 to-navy-900 dark:from-gold-700 dark:to-gold-600 relative overflow-hidden'>
        {/* Decorative elements */}
        <div className='absolute top-0 left-0 w-64 h-64 bg-white/5 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2'></div>
        <div className='absolute bottom-0 right-0 w-80 h-80 bg-white/5 rounded-full blur-3xl transform translate-x-1/3 translate-y-1/3'></div>
        <div className='container mx-auto px-6'>
          <div className='max-w-4xl mx-auto text-center'>
            <div className='bg-white/10 backdrop-blur-sm rounded-2xl p-12 border border-white/20 dark:bg-navy-900/20 dark:border-navy-800/30 shadow-xl hover:shadow-2xl transition-all duration-500 relative z-10'>
              <CheckCircle className='w-16 h-16 text-green-400 mx-auto mb-6' />
              <h2 className='text-3xl font-bold text-white mb-4'>Thank You!</h2>
              <p className='text-navy-100 text-lg'>
                You've successfully subscribed to our newsletter. You'll receive
                updates about our programs, success stories, and ways to get
                involved in our mission.
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      className='py-12 bg-gradient-to-br from-white via-amber-50/30 to-white
    dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative
    overflow-hidden'
    >
      <div className='container mx-auto px-6'>
        <div className='max-w-4xl mx-auto text-center'>
          <div
            className=' bg-gradient-to-br from-amber-100/80 to-yellow-50/80
          dark:from-amber-900/20 dark:to-yellow-900/20  rounded-2xl p-12 '
          >
            <Mail
              className='w-16 h-16 text-gold-400 dark:text-amber-400 mx-auto mb-6 animate-pulse'
              style={{ animationDuration: '3s' }}
            />
            <h2 className='text-4xl font-bold text-navy-800 dark:text-gold-600 mb-6'>
              Stay Connected
            </h2>
            <p className='text-navy-700 dark:text-white text-xl mb-8 max-w-2xl mx-auto'>
              Get the latest updates on our programs, inspiring success stories,
              and opportunities to make a difference in women's lives.
            </p>

            <form onSubmit={handleSubmit} className='max-w-md mx-auto'>
              <div className='flex flex-col sm:flex-row gap-4'>
                <input
                  type='email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder='Enter your email address'
                  required
                  className='flex-1 px-6 py-4 rounded-xl bg-navy backdrop-blur-sm border border-gold text-navy placeholder-navy-200 dark:bg-navy-800/30 dark:border-navy-700/30 dark:text-amber-100 dark:placeholder-navy-300 focus:outline-none focus:ring-2 focus:ring-gold-400 dark:focus:ring-navy-400 focus:border-transparent'
                />

                <Button type='submit' disabled={isLoading}>
                  {isLoading ? (
                    <div className='flex items-center justify-center'>
                      <Loader2 className='w-5 h-5 animate-spin text-white' />
                    </div>
                  ) : (
                    'Subscribe'
                  )}
                </Button>
              </div>
            </form>

            <p className='text-gray text-sm mt-4'>
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Newsletter;
