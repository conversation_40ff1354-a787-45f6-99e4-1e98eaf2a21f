import React, { useState } from 'react';
import { Heart, Users, BookOpen, Home, DollarSign } from 'lucide-react';

const Donation = () => {
  const [selectedAmount, setSelectedAmount] = useState(50);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedCause, setSelectedCause] = useState('general');

  const predefinedAmounts = [25, 50, 100, 250, 500];

  const causes = [
    {
      id: 'general',
      name: 'General Support',
      icon: Heart,
      description: 'Support all our programs and operations',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 'training',
      name: 'Skills Training',
      icon: BookOpen,
      description: 'Fund training programs for women',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'counseling',
      name: 'Counseling Services',
      icon: Users,
      description: 'Support trauma recovery programs',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'shelter',
      name: 'Safe Housing',
      icon: Home,
      description: 'Provide safe spaces for women in need',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  const impactMessages = {
    25: 'Provides basic supplies for one woman in our training program',
    50: 'Funds one counseling session for a trauma survivor',
    100: 'Supports one woman through a complete skills training course',
    250: 'Provides startup materials for a small business venture',
    500: 'Sponsors comprehensive support for one woman for a month'
  };

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setSelectedAmount(0);
  };

  const getCurrentAmount = () => {
    return customAmount ? parseInt(customAmount) : selectedAmount;
  };

  const getImpactMessage = () => {
    const amount = getCurrentAmount();
    if (amount >= 500) return impactMessages[500];
    if (amount >= 250) return impactMessages[250];
    if (amount >= 100) return impactMessages[100];
    if (amount >= 50) return impactMessages[50];
    if (amount >= 25) return impactMessages[25];
    return 'Every contribution makes a difference in a woman\'s life';
  };

  return (
    <section id="donate" className="py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-bold text-gray-800 mb-8 tracking-tight">
              Make a Difference
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-yellow-500 mx-auto mb-8 rounded-full"></div>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
              Your donation directly impacts women's lives, providing them with the tools, 
              support, and opportunities they need to build independent, dignified futures.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Donation Form */}
            <div className="bg-white rounded-3xl shadow-2xl p-10 border border-gray-100">
              <h3 className="text-3xl font-bold text-gray-800 mb-8">Choose Your Impact</h3>
              
              {/* Cause Selection */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-700 mb-4">Select a Cause</h4>
                <div className="grid grid-cols-2 gap-3">
                  {causes.map((cause) => (
                    <button
                      key={cause.id}
                      onClick={() => setSelectedCause(cause.id)}
                      className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                        selectedCause === cause.id
                          ? 'border-navy-800 bg-navy-50'
                          : 'border-gray-200 hover:border-navy-300'
                      }`}
                    >
                      <div className="flex items-center mb-2">
                        <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${cause.color} flex items-center justify-center mr-3`}>
                          <cause.icon className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-semibold text-gray-800">{cause.name}</span>
                      </div>
                      <p className="text-sm text-gray-600">{cause.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Amount Selection */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-700 mb-4">Donation Amount</h4>
                <div className="grid grid-cols-3 gap-3 mb-4">
                  {predefinedAmounts.map((amount) => (
                    <button
                      key={amount}
                      onClick={() => handleAmountSelect(amount)}
                      className={`p-4 rounded-xl border-2 font-semibold transition-all duration-300 ${
                        selectedAmount === amount
                          ? 'border-navy-800 bg-navy-800 text-white'
                          : 'border-gray-200 text-gray-700 hover:border-navy-300'
                      }`}
                    >
                      ${amount}
                    </button>
                  ))}
                </div>
                
                <div className="relative">
                  <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="number"
                    placeholder="Custom amount"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy-800 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Impact Message */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
                <h4 className="font-semibold text-yellow-800 mb-2">Your Impact</h4>
                <p className="text-yellow-700">{getImpactMessage()}</p>
              </div>

              {/* Donate Button */}
              <button className="w-full bg-gradient-to-r from-navy-800 to-navy-900 text-white py-4 rounded-xl font-semibold text-lg hover:from-navy-900 hover:to-black transition-all duration-500 transform hover:scale-105 shadow-xl">
                Donate ${getCurrentAmount() || '0'} Now
              </button>
              
              <p className="text-center text-gray-500 text-sm mt-4">
                Secure payment processing • Tax-deductible receipt provided
              </p>
            </div>

            {/* Impact Stories */}
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200">
                <h4 className="text-2xl font-bold text-blue-800 mb-6">Impact Stories</h4>
                
                {/* Story 1 */}
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    MT
                  </div>
                  <div>
                    <h5 className="font-semibold text-blue-800 mb-2">Maria's Transformation</h5>
                    <p className="text-blue-700 text-sm leading-relaxed mb-2">
                      "Thanks to WOPEDE's sewing program, I now run my own tailoring business. 
                      I can support my three children and even employ two other women from my community."
                    </p>
                    <span className="text-xs text-blue-600 font-medium bg-blue-100 px-2 py-1 rounded-full">Sewing Program Graduate</span>
                  </div>
                </div>

                {/* Story 2 */}
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    SN
                  </div>
                  <div>
                    <h5 className="font-semibold text-blue-800 mb-2">Sarah's New Beginning</h5>
                    <p className="text-blue-700 text-sm leading-relaxed mb-2">
                      "After trauma counseling and catering training, I opened my bakery. 
                      Now I provide for my family with dignity and hope for the future."
                    </p>
                    <span className="text-xs text-purple-600 font-medium bg-purple-100 px-2 py-1 rounded-full">Catering Program Graduate</span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border border-green-200">
                <h4 className="text-2xl font-bold text-green-800 mb-6">Other Ways to Help</h4>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-green-700 font-medium">Volunteer your time and skills</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <BookOpen className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-green-700 font-medium">Sponsor a training program</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                      <Heart className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-green-700 font-medium">Spread awareness in your community</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Donation;