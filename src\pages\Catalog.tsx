import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Search, Filter, Grid, List, X } from 'lucide-react';
import ProductCard from '../components/ProductCard';
import Gallery from '../components/CatalogGallery';
import { products, categories, galleryItems } from '../data/products';
import { Product } from '../types';

const Catalog: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(
    searchParams.get('category') || ''
  );
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [showFilters, setShowFilters] = useState(false);

  // Background slideshow
  const backgroundImages = [
    '/assets/bus1.jpg',
    '/assets/bus2.jpg',
    '/assets/bus3.jpg',
    '/assets/bus4.jpg',
  ];
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % backgroundImages.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [backgroundImages.length]);

  const filteredProducts = useMemo(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          product.tags?.some((tag) =>
            tag.toLowerCase().includes(searchTerm.toLowerCase())
          )
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(
        (product) => product.category === selectedCategory
      );
    }

    // Filter by price range
    filtered = filtered.filter(
      (product) =>
        product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'popularity':
          return b.reviews - a.reviews;
        case 'newest':
          return (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [searchTerm, selectedCategory, sortBy, priceRange]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (category) {
      setSearchParams({ category });
    } else {
      setSearchParams({});
    }
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setPriceRange([0, 100]);
    setSearchParams({});
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-navy-900 dark:via-navy-800 dark:to-navy-900'>
      {/* Hero Section */}
      <section className='relative h-80 bg-gradient-to-br from-navy-900 via-navy-800 to-navy-700 overflow-hidden'>
        {/* Background Slides */}
        <div className='absolute inset-0'>
          {backgroundImages.map((bgImage, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-all duration-1000 ${
                index === currentSlide
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-105'
              }`}
            >
              <img
                src={bgImage}
                alt={`Background ${index + 1}`}
                className='w-full h-full object-cover'
              />
              <div className='absolute inset-0 bg-gradient-to-r from-navy-900/90 via-navy-800/70 to-navy-700/50'></div>
            </div>
          ))}
        </div>

        {/* Content */}
        <div className='relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center'>
          <div className='text-center w-full'>
            <div className='inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md rounded-full border border-white/20 shadow-xl mb-6'>
              <span className='text-gold-300 font-semibold'>
                🛍️ Premium Collection
              </span>
            </div>
            <h1 className='text-4xl md:text-6xl font-bold text-white leading-tight mb-4'>
              Product{' '}
              <span className='text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500'>
                Catalog
              </span>
            </h1>
            <p className='text-xl text-gold-100 max-w-2xl mx-auto leading-relaxed'>
              Discover our complete collection of handcrafted products made by
              skilled women artisans.
            </p>
          </div>
        </div>
      </section>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
        {/* Filters and Search */}
        <div className='bg-white/80 dark:bg-navy-800/80 backdrop-blur-md rounded-2xl shadow-xl p-6 mb-8 border border-gray-200/50 dark:border-navy-700/50'>
          <div className='flex flex-col lg:flex-row gap-4'>
            {/* Search */}
            <div className='relative flex-1'>
              <Search className='absolute left-4 top-4 h-5 w-5 text-gray-400' />
              <input
                type='text'
                placeholder='Search products...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-navy-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300'
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className='px-4 py-3 border border-gray-300 dark:border-navy-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white transition-all duration-300'
            >
              <option value=''>All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className='px-4 py-3 border border-gray-300 dark:border-navy-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white transition-all duration-300'
            >
              <option value='name'>Sort by Name</option>
              <option value='price-low'>Price: Low to High</option>
              <option value='price-high'>Price: High to Low</option>
              <option value='rating'>Highest Rated</option>
              <option value='popularity'>Most Popular</option>
              <option value='newest'>Newest First</option>
            </select>

            {/* View Mode */}
            <div className='flex items-center space-x-2'>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-3 rounded-xl transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-gold-500 text-white shadow-lg'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gold-500 hover:bg-gray-100 dark:hover:bg-navy-700'
                }`}
              >
                <Grid className='h-5 w-5' />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-3 rounded-xl transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-gold-500 text-white shadow-lg'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gold-500 hover:bg-gray-100 dark:hover:bg-navy-700'
                }`}
              >
                <List className='h-5 w-5' />
              </button>
            </div>
          </div>
        </div>

        {/* Results Header */}
        <div className='mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <p className='text-lg text-gray-700 dark:text-gray-300'>
            Showing{' '}
            <span className='font-bold text-gold-600 dark:text-gold-400'>
              {filteredProducts.length}
            </span>{' '}
            of <span className='font-bold'>{products.length}</span> products
          </p>

          {(selectedCategory || searchTerm) && (
            <div className='flex items-center gap-2'>
              {selectedCategory && (
                <span className='px-4 py-2 bg-gold-100 dark:bg-gold-900/30 text-gold-800 dark:text-gold-300 rounded-full font-semibold text-sm border border-gold-200 dark:border-gold-700'>
                  {categories.find((c) => c.id === selectedCategory)?.name}
                </span>
              )}
              <button
                onClick={clearAllFilters}
                className='flex items-center gap-1 px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full text-sm hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors border border-red-200 dark:border-red-700'
              >
                <X className='h-4 w-4' />
                Clear Filters
              </button>
            </div>
          )}
        </div>

        {/* Products Grid */}
        <div
          className={`grid gap-6 transition-all duration-500 ease-in-out ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}
        >
          {filteredProducts.map((product, index) => (
            <div
              key={product.id}
              className='transform transition-all duration-300'
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className='text-center py-20'>
            <div className='text-gray-400 dark:text-gray-500 mb-6'>
              <Filter className='h-16 w-16 mx-auto' />
            </div>
            <h3 className='text-2xl font-bold text-gray-800 dark:text-white mb-4'>
              No products found
            </h3>
            <p className='text-lg text-gray-600 dark:text-gray-400 mb-8'>
              Try adjusting your search or filter criteria
            </p>
            <button
              onClick={clearAllFilters}
              className='px-8 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg'
            >
              Clear All Filters
            </button>
          </div>
        )}

        {/* Gallery Section */}
        {filteredProducts.length > 0 && (
          <div className='mt-20'>
            <Gallery
              items={galleryItems}
              autoSlide={true}
              slideInterval={4000}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Catalog;
