import React from 'react';
import { ProductGallery } from './ProductGallery';
import { PartnershipsSection } from './PartnershipSection';
import AnimatedSection from './AnimatedSection';

const Business = () => {
  return (
    <section
      id='business'
      className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      {/* Elegant Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div className='absolute top-32 left-16 w-64 h-64 bg-gradient-to-r from-amber-300/10 to-yellow-300/10 rounded-full blur-3xl animate-morph' />
        <div
          className='absolute bottom-32 right-16 w-80 h-80 bg-gradient-to-r from-navy-300/8 to-navy-400/8 rounded-full blur-3xl animate-morph'
          style={{ animationDelay: '4s' }}
        />
      </div>

      <div className='container mx-auto px-6 relative z-10'>
        <div className='max-w-6xl mx-auto'>
          {/* Enhanced section header */}
          <div className='grid lg:grid-cols-3 gap-12 mb-20 items-start'>
            {/* Main intro content */}
            <div className='lg:col-span-2'>
              <AnimatedSection>
                <div className='text-center lg:text-left'>
                  <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-8 tracking-tight gradient-text-hero'>
                    Our Business
                  </h2>
                  <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 mx-auto lg:mx-0 mb-8 rounded-full' />
                  <p className='text-elegant text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto lg:mx-0 leading-relaxed'>
                    WOPEDE creates beautiful handcrafted products while
                    empowering women with sustainable income opportunities.
                    Every purchase supports our mission of peace and
                    development.
                  </p>
                </div>
              </AnimatedSection>
            </div>

            {/* Enhanced Inspirational Quote */}
            <div className='lg:col-span-1'>
              <AnimatedSection delay={300}>
                <div className='card-elegant p-6 h-fit relative overflow-hidden'>
                  <div className='absolute inset-0 bg-gradient-to-br from-amber-50/80 to-yellow-50/80 dark:from-amber-900/20 dark:to-yellow-900/20' />
                  <div className='relative z-10 text-center'>
                    <div className='text-3xl text-amber-400 mb-4 animate-float-gentle'>
                      "
                    </div>
                    <blockquote className='text-elegant text-gray-700 dark:text-gray-300 leading-relaxed mb-6 italic'>
                      In every thread we weave, we are crafting hope, building
                      futures, and painting tomorrow with colors of possibility.
                    </blockquote>
                    <div className='flex items-center justify-center space-x-3'>
                      <div className='w-12 h-12 rounded-full overflow-hidden shadow-lg ring-2 ring-amber-200 dark:ring-amber-700'>
                        <img
                          src='/assets/founder1.jpg'
                          alt='Cimpaye Modeste'
                          className='w-full h-full object-cover'
                        />
                      </div>
                      <div className='text-left'>
                        <p className='font-bold text-gray-800 dark:text-white text-sm'>
                          Cimpaye Modeste
                        </p>
                        <p className='text-amber-600 dark:text-amber-400 font-semibold text-xs'>
                          Founder, WOPEDE CBO
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </div>

          {/* Product Gallery Section */}
          <AnimatedSection delay={600}>
            <div className='mb-20'>
              <ProductGallery />
            </div>
          </AnimatedSection>

          {/* Partnerships Section */}
          <AnimatedSection delay={900}>
            <PartnershipsSection />
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default Business;
