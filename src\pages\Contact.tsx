import React from 'react';
import Layout from '../components/Layout';
import Contact from '../components/Contact';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';

const ContactPage: React.FC = () => {
  const contactInfo = [
    {
      icon: MapPin,
      title: 'Our Location',
      details: ['123 Peace Avenue', 'Community Center', 'Nairobi, Kenya'],
      color: 'from-navy-500 to-skyblue-500',
    },
    {
      icon: Phone,
      title: 'Phone Numbers',
      details: ['+254 700 123 456', '+254 720 987 654', 'Emergency: +254 711 000 111'],
      color: 'from-skyblue-500 to-navy-500',
    },
    {
      icon: Mail,
      title: 'Email Addresses',
      details: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      color: 'from-navy-600 to-skyblue-400',
    },
    {
      icon: Clock,
      title: 'Office Hours',
      details: ['Monday - Friday: 8:00 AM - 5:00 PM', 'Saturday: 9:00 AM - 2:00 PM', 'Sunday: Closed'],
      color: 'from-skyblue-600 to-navy-400',
    },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-navy-600 via-skyblue-600 to-navy-700 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/assets/bus4.jpg"
            alt="Contact WOPEDE"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-navy-900/80 to-skyblue-900/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Get in{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-skyblue-200 to-white">
              Touch
            </span>
          </h1>
          <p className="text-xl text-skyblue-100 max-w-3xl mx-auto leading-relaxed">
            We're here to help and answer any questions you might have. 
            Reach out to us and we'll respond as soon as we can.
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              How to Reach Us
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Multiple ways to connect with our team. Choose the method that works best for you.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className="text-center p-6 bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex justify-center mb-4">
                  <div className={`p-4 bg-gradient-to-br ${info.color} rounded-full`}>
                    <info.icon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-navy-900 dark:text-white mb-4">
                  {info.title}
                </h3>
                <div className="space-y-2">
                  {info.details.map((detail, detailIndex) => (
                    <p key={detailIndex} className="text-gray-700 dark:text-gray-300">
                      {detail}
                    </p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <Contact />
        </div>
      </section>

      {/* Map Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Find Us Here
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Visit our community center and see firsthand the impact we're making together.
            </p>
          </div>

          <div className="bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg overflow-hidden">
            <div className="aspect-w-16 aspect-h-9">
              <div className="w-full h-96 bg-gradient-to-br from-navy-100 to-skyblue-100 dark:from-navy-600 dark:to-navy-700 flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="h-16 w-16 text-navy-400 dark:text-skyblue-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-navy-900 dark:text-white mb-2">
                    Interactive Map
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    Map integration coming soon
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Contact */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Need Immediate Help?
          </h2>
          <p className="text-xl text-navy-100 mb-8 max-w-2xl mx-auto">
            If you're in crisis or need immediate support, don't hesitate to reach out. 
            We're here for you 24/7.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:+254711000111"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Phone className="mr-2 h-5 w-5" />
              Emergency Hotline
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-navy-600 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              <Mail className="mr-2 h-5 w-5" />
              Email Support
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ContactPage;
