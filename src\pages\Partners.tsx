import React from 'react';
import Layout from '../components/Layout';
import { Handshake, Globe, Heart, Users, Award, Building } from 'lucide-react';

const Partners: React.FC = () => {
  const partnerCategories = [
    {
      icon: Building,
      title: 'Corporate Partners',
      description: 'Companies supporting our mission through funding and resources',
      partners: [
        { name: 'Kenya Commercial Bank', type: 'Financial Services', support: 'Microfinance Program' },
        { name: 'Safaricom Foundation', type: 'Telecommunications', support: 'Digital Literacy Training' },
        { name: 'Equity Bank', type: 'Banking', support: 'Women Entrepreneurship Fund' },
      ],
      color: 'from-navy-500 to-skyblue-500',
    },
    {
      icon: Globe,
      title: 'International Organizations',
      description: 'Global partners working towards women empowerment',
      partners: [
        { name: 'UN Women', type: 'United Nations', support: 'Gender Equality Programs' },
        { name: 'World Bank', type: 'Development Finance', support: 'Community Development Projects' },
        { name: 'USAID', type: 'Development Agency', support: 'Capacity Building Programs' },
      ],
      color: 'from-skyblue-500 to-navy-500',
    },
    {
      icon: Heart,
      title: 'NGO Partners',
      description: 'Non-profit organizations sharing our vision',
      partners: [
        { name: 'Kenya Red Cross', type: 'Humanitarian', support: 'Emergency Response Training' },
        { name: 'Plan International', type: 'Child Rights', support: 'Girls Education Programs' },
        { name: 'ActionAid Kenya', type: 'Development', support: 'Women Rights Advocacy' },
      ],
      color: 'from-navy-600 to-skyblue-400',
    },
    {
      icon: Users,
      title: 'Community Partners',
      description: 'Local organizations and community groups',
      partners: [
        { name: 'Maendeleo ya Wanawake', type: 'Women\'s Group', support: 'Grassroots Mobilization' },
        { name: 'Kenya Association of Women Business Owners', type: 'Business Network', support: 'Mentorship Programs' },
        { name: 'Local Churches Coalition', type: 'Faith-Based', support: 'Community Outreach' },
      ],
      color: 'from-skyblue-600 to-navy-400',
    },
  ];

  const partnershipBenefits = [
    {
      icon: Handshake,
      title: 'Strategic Collaboration',
      description: 'Work together on initiatives that create lasting impact in communities.',
    },
    {
      icon: Award,
      title: 'Shared Recognition',
      description: 'Gain visibility and recognition for your commitment to social responsibility.',
    },
    {
      icon: Users,
      title: 'Network Expansion',
      description: 'Connect with like-minded organizations and expand your professional network.',
    },
    {
      icon: Heart,
      title: 'Meaningful Impact',
      description: 'Make a real difference in the lives of women and their communities.',
    },
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-navy-600 via-skyblue-600 to-navy-700 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/assets/bus3.jpg"
            alt="WOPEDE Partners"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-navy-900/80 to-skyblue-900/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Our{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-skyblue-200 to-white">
              Partners
            </span>
          </h1>
          <p className="text-xl text-skyblue-100 max-w-3xl mx-auto leading-relaxed">
            Together, we're stronger. Our partnerships enable us to reach more women, 
            create greater impact, and build sustainable change in communities.
          </p>
        </div>
      </section>

      {/* Partnership Philosophy */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Stronger{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-navy-600 to-skyblue-600">
                Together
              </span>
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              We believe that meaningful partnerships are built on shared values, mutual respect, 
              and a common commitment to empowering women and strengthening communities. Our 
              collaborative approach ensures that together, we can achieve more than we ever could alone.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {partnershipBenefits.map((benefit, index) => (
              <div
                key={index}
                className="text-center p-6 bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-gradient-to-br from-navy-500 to-skyblue-500 rounded-full">
                    <benefit.icon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-navy-900 dark:text-white mb-3">
                  {benefit.title}
                </h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Categories */}
      <section className="py-20 bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Our Partner Network
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              We work with diverse organizations across different sectors to maximize our impact.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {partnerCategories.map((category, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center mb-6">
                  <div className={`p-4 bg-gradient-to-br ${category.color} rounded-2xl mr-4`}>
                    <category.icon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-navy-900 dark:text-white">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {category.description}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {category.partners.map((partner, partnerIndex) => (
                    <div
                      key={partnerIndex}
                      className="flex justify-between items-center p-4 bg-white/50 dark:bg-navy-600/50 rounded-xl"
                    >
                      <div>
                        <h4 className="font-bold text-navy-900 dark:text-white">
                          {partner.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {partner.type}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-skyblue-600 dark:text-skyblue-400 font-medium">
                          {partner.support}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Impact */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 dark:text-white mb-6">
              Partnership Impact
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              Our partnerships have enabled us to achieve remarkable results and expand our reach.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { metric: '$2.5M+', label: 'Partnership Funding Secured' },
              { metric: '50+', label: 'Active Partnerships' },
              { metric: '15+', label: 'Countries Represented' },
              { metric: '25+', label: 'Years of Collaboration' },
            ].map((stat, index) => (
              <div
                key={index}
                className="text-center p-6 bg-gradient-to-br from-white to-skyblue-50/50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-lg"
              >
                <div className="text-4xl font-bold text-skyblue-600 dark:text-skyblue-400 mb-2">
                  {stat.metric}
                </div>
                <div className="text-gray-700 dark:text-gray-300 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Become a Partner */}
      <section className="py-20 bg-gradient-to-r from-navy-600 to-skyblue-600">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Become Our Partner
          </h2>
          <p className="text-xl text-navy-100 mb-8 max-w-2xl mx-auto">
            Join us in creating lasting change. Whether you're a corporation, NGO, 
            or community organization, there's a place for you in our mission.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 bg-white text-navy-600 hover:bg-gray-100 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Handshake className="mr-2 h-5 w-5" />
              Partner With Us
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-navy-600 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              Learn More
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Partners;
