import React, { useState, useEffect } from 'react';
import { Menu, X, Sun, Moon } from 'lucide-react';
import { useTheme } from './ThemeProvider';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offset = 80;
      const elementPosition = element.offsetTop - offset;
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth',
      });
    }
    setIsMenuOpen(false);
  };

  const navLinks = [
    { id: 'home', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'programs', label: 'Programs' },
    { id: 'gallery', label: 'Gallery' },
    { id: 'business', label: 'Products' },
    { id: 'impact', label: 'Impact' },
    { id: 'contact', label: 'Contact' },
  ];

  return (
    <>
      {/* Fixed Theme Toggle for Mobile */}
      <button
        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        className="md:hidden fixed bottom-6 right-6 z-50 p-3 rounded-full shadow-md bg-gold-500 dark:bg-navy-700 text-navy-900 dark:text-gold-400 border border-gold-400 dark:border-gold-500"
        aria-label="Toggle theme"
      >
        {theme === 'dark' ? (
          <Sun className="w-5 h-5" />
        ) : (
          <Moon className="w-5 h-5" />
        )}
      </button>

      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-in-out ${
          isScrolled
            ? 'bg-white/95 dark:bg-navy-900/95 backdrop-blur-lg shadow-2xl border-b border-gray-100 dark:border-navy-700'
            : 'bg-transparent'
        }`}
      >
        <div className='container mx-auto px-4 sm:px-6'>
          <div className='flex items-center justify-between h-20'>
            {/* Enhanced Logo section */}
            <div
              className='flex items-center space-x-4 cursor-pointer group'
              onClick={() => scrollToSection('home')}
            >
              <div className='w-14 h-14 bg-gradient-to-br from-gold-500 to-gold-400 dark:from-gold-500 dark:to-gold-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-all duration-500 shadow-xl border border-gold-300 dark:border-gold-600'>
                <span className='text-navy-900 dark:text-navy-900 font-bold text-2xl'>
                  W
                </span>
              </div>
              <div className='hidden sm:block'>
                <h1
                  className={`font-bold text-2xl transition-all duration-500 ${
                    isScrolled
                      ? 'text-navy-800 dark:text-white'
                      : 'text-amber dark:text-white'
                  } group-hover:scale-105`}
                >
                  WOPEDE
                </h1>
                <p
                  className={`text-sm transition-all duration-500 font-medium ${
                    isScrolled
                      ? 'text-navy-600 dark:text-gold-300'
                      : 'text-gold-500 dark:text-gold-300'
                  }`}
                >
                  Women for Peace & Development
                </p>
              </div>
            </div>

            {/* Enhanced Desktop Navigation */}
            <div className='hidden md:flex items-center space-x-8'>
              {navLinks.map((link, index) => (
                <button
                  key={link.id}
                  onClick={() => scrollToSection(link.id)}
                  className={`font-semibold transition-all duration-500 hover:scale-105 relative group ${
                    isScrolled
                      ? 'text-gold-600 dark:text-gold-400 hover:text-gold-700 dark:hover:text-gold-300'
                      : 'text-navy dark:text-gold-300 hover:text-gold-500 dark:hover:text-gold-400'
                  }`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {link.label}
                  <span className='absolute -bottom-1 left-0 w-0 h-0.5 bg-current transition-all duration-500 group-hover:w-full'></span>
                </button>
              ))}
              {/* Theme Toggle Button */}
              <button
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className={`p-3 rounded-full transition-all duration-300 transform hover:scale-110 ${
                  isScrolled
                    ? 'bg-gray-300 text-navy-800 hover:bg-gray-200'
                    : 'bg-amber-500 text-white hover:bg-navy/10'
                } dark:bg-navy-800 dark:text-gold-400 dark:hover:bg-navy-700`}
                aria-label='Toggle theme'
              >
                {theme === 'dark' ? (
                  <Sun className='w-5 h-5' />
                ) : (
                  <Moon className='w-5 h-5' />
                )}
              </button>

              <button
                onClick={() => scrollToSection('contact')}
                className={`px-8 py-3 rounded-xl font-semibold transition-all duration-500 transform hover:scale-105 hover:shadow-xl border-2 ${
                  isScrolled
                    ? 'bg-navy-800 text-white hover:bg-navy-900 border-navy-800'
                    : 'bg-gold-400 text-navy-900 hover:bg-gold-300 border-gold-400'
                } dark:bg-gold-500 dark:text-navy-900 dark:hover:bg-gold-400 dark:border-gold-500`}
              >
                Get Involved
              </button>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`md:hidden p-3 rounded-xl transition-all duration-500 transform hover:scale-110 ${
                isScrolled
                  ? 'text-gray-700 dark:text-gold-300 hover:bg-gray-100 dark:hover:bg-navy-700'
                  : 'text-navy-800 dark:text-white hover:bg-white/10 dark:hover:bg-navy-700/50'
              }`}
            >
              <div className='relative w-6 h-6'>
                <Menu
                  size={24}
                  className={`absolute inset-0 transition-all duration-500 ${
                    isMenuOpen ? 'opacity-0 rotate-45' : 'opacity-100 rotate-0'
                  }`}
                />
                <X
                  size={24}
                  className={`absolute inset-0 transition-all duration-500 ${
                    isMenuOpen ? 'opacity-100 rotate-0' : 'opacity-0 -rotate-45'
                  }`}
                />
              </div>
            </button>
          </div>

          {/* Enhanced Mobile Navigation */}
          <div
            className={`md:hidden overflow-hidden transition-all duration-700 ease-in-out ${
              isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className='bg-white/98 dark:bg-navy-900/98 backdrop-blur-lg border-t border-gray-100 dark:border-navy-700/50 py-6 rounded-b-2xl shadow-2xl'>
              <div className='flex flex-col space-y-3'>
                {navLinks.map((link, index) => (
                  <button
                    key={link.id}
                    onClick={() => scrollToSection(link.id)}
                    className='text-left px-6 py-4 text-navy-700 dark:text-gold-300 hover:text-gold-600 dark:hover:text-gold-400 hover:bg-gold-50 dark:hover:bg-navy-800 rounded-xl transition-all duration-500 transform hover:translate-x-2 font-medium'
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    {link.label}
                  </button>
                ))}
                <div className='flex items-center justify-center mx-6 mt-4'>
                  <button
                    onClick={() => scrollToSection('contact')}
                    className='w-full px-8 py-4 bg-navy-800 text-white rounded-xl font-semibold hover:bg-navy-900 transition-all duration-500 transform hover:scale-105 shadow-lg dark:bg-gold-500 dark:text-navy-900 dark:hover:bg-gold-400'
                  >
                    Get Involved
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;