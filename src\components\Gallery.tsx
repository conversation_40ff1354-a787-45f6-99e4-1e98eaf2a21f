import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Heart, Sparkles } from 'lucide-react';
import Button from './Button';

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(0);

  // All gallery images
  const galleryImages = [
    'bus1.jpg',
    'bus2.jpg',
    'bus3.jpg',
    'bus4.jpg',
    'bus5.jpg',
    'bus7.jpg',
    'bus8.jpg',
    'bus9.jpg',
    'training1.jpg',
    'training2.jpg',
    'training3.jpg',
    'art1.jpg',
    'waving.jpg',
    'stakeholders.jpg',
    'founder1.jpg',
  ];

  const nextImage = () => {
    setSelectedImage((prev) => (prev + 1) % galleryImages.length);
  };

  const prevImage = () => {
    setSelectedImage(
      (prev) => (prev - 1 + galleryImages.length) % galleryImages.length
    );
  };

  return (
    <section
      id='gallery'
      className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      {/* Elegant Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div className='absolute top-32 left-16 w-64 h-64 bg-gradient-to-r from-gold-300/10 to-gold-300/10 rounded-full blur-3xl animate-morph' />
        <div
          className='absolute bottom-32 right-16 w-80 h-80 bg-gradient-to-r from-navy-300/8 to-navy-400/8 rounded-full blur-3xl animate-morph'
          style={{ animationDelay: '4s' }}
        />
      </div>
      <div className='container mx-auto px-6 relative z-10'>
        <div className='max-w-7xl mx-auto'>
          {/* Captivating Header */}
          <div className='text-center mb-12'>
            <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-navy-800/80 backdrop-blur-md px-8 py-4 rounded-full border border-gold-200 dark:border-gold-700/30 shadow-lg mb-8'>
              <Sparkles className='w-5 h-5 text-gold-500 dark:text-gold-400' />
              <span className='text-navy-800 dark:text-gold-300 font-semibold text-sm tracking-wide'>
                Stories in Every Frame
              </span>
            </div>

            <h2 className='font-serif text-5xl md:text-6xl font-light leading-tight text-navy-800 dark:text-white mb-5'>
              Where Dreams
              <span className='block bg-gradient-to-r from-gold-500 via-gold-400 to-gold-500 dark:from-gold-400 dark:via-gold-300 dark:to-gold-400 bg-clip-text text-transparent'>
                Take Shape
              </span>
            </h2>

            <div className='max-w-4xl mx-auto'>
              <p className='text-l md:text-xl text-navy-700 dark:text-white leading-relaxed font-light mb-8'>
                Every image tells a story of transformation. From skilled hands
                crafting beautiful products to hearts healing through community
                support, witness the magic that happens when women are empowered
                to dream, create, and thrive.
              </p>

              <div className='flex items-center justify-center space-x-5 text-navy-600 dark:text-gold-300'>
                <div className='flex items-center space-x-2'>
                  <Heart className='w-5 h-5 text-red-400' />
                  <span className='font-medium'>Stories of Hope</span>
                </div>
                <div className='w-2 h-2 bg-amber-400 rounded-full'></div>
                <div className='flex items-center space-x-2'>
                  <Sparkles className='w-5 h-5 text-amber-400' />
                  <span className='font-medium'>Moments of Joy</span>
                </div>
                <div className='w-2 h-2 bg-amber-400 rounded-full'></div>
                <div className='flex items-center space-x-2'>
                  <span className='text-1xl'>✨</span>
                  <span className='font-medium'>Dreams Realized</span>
                </div>
              </div>
            </div>
          </div>

          {/* Optimized Gallery Layout */}
          <div className='mb-10'>
            {/* Featured Image */}
            <div className='relative group mb-6'>
              <div className='aspect-[16/9] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-gray-100 to-gray-200 max-w-4xl mx-auto'>
                <img
                  src={`/assets/${galleryImages[selectedImage]}`}
                  alt='Featured gallery image'
                  className='w-full h-full object-cover transition-transform duration-700 group-hover:scale-105'
                />
                <div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent'></div>
              </div>

              {/* Navigation Arrows */}
              <button
                onClick={prevImage}
                className='absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-all duration-300 transform hover:scale-110'
              >
                <ChevronLeft className='w-6 h-6 text-gray-700' />
              </button>

              <button
                onClick={nextImage}
                className='absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-all duration-300 transform hover:scale-110'
              >
                <ChevronRight className='w-6 h-6 text-gray-700' />
              </button>

              {/* Image Counter */}
              <div className='absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium'>
                {selectedImage + 1} / {galleryImages.length}
              </div>
            </div>

            {/* Compact Thumbnail Grid */}
            <div className='max-w-5xl mx-auto'>
              <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-4 text-center'>
                Gallery Collection
              </h3>
              <div className='grid grid-cols-5 md:grid-cols-8 lg:grid-cols-10 gap-2'>
                {galleryImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-square rounded-lg overflow-hidden transition-all duration-300 transform hover:scale-105 ${
                      selectedImage === index
                        ? 'ring-3 ring-amber-400 shadow-lg'
                        : 'hover:shadow-md opacity-70 hover:opacity-100'
                    }`}
                  >
                    <img
                      src={`/assets/${image}`}
                      alt={`Gallery thumbnail ${index + 1}`}
                      className='w-full h-full object-cover'
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className='text-center mt-10'>
            <p className='text-xl text-navy-700 dark:text-white mb-8 font-light'>
              Ready to be part of these inspiring stories?
            </p>
            <div className='flex flex-col sm:flex-row gap-4 justify-center'>
              <Button href='#contact'>
                <div className='flex items-center space-x-2'>
                  <span>Join Our Mission</span>
                  <span>→</span>
                </div>
              </Button>
              <a
                href='#business'
                className='inline-flex items-center space-x-3 bg-white dark:bg-navy-700 border-2 border-navy-800 dark:border-navy-600 text-navy-800 dark:text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-navy-50 dark:hover:bg-navy-600 transition-all duration-500 transform hover:scale-105'
              >
                <Sparkles className='w-5 h-5' />
                <span>Explore Our Products</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #fbbf24;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #f59e0b;
        }
      `}</style>
    </section>
  );
};

export default Gallery;
