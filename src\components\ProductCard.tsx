import React from 'react';
import { Star, ShoppingCart, Heart, Eye } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../contexts/CartContext';

interface ProductCardProps {
  product: Product;
  onQuickView?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onQuickView }) => {
  const { addToCart } = useCart();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addToCart(product);
  };

  const getAvailabilityColor = () => {
    switch (product.availability) {
      case 'in-stock':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200 dark:text-emerald-400 dark:bg-emerald-900/30 dark:border-emerald-700';
      case 'limited':
        return 'text-orange-600 bg-orange-50 border-orange-200 dark:text-orange-400 dark:bg-orange-900/30 dark:border-orange-700';
      case 'out-of-stock':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/30 dark:border-red-700';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-800 dark:border-gray-600';
    }
  };

  const getAvailabilityText = () => {
    switch (product.availability) {
      case 'in-stock':
        return 'In Stock';
      case 'limited':
        return 'Limited';
      case 'out-of-stock':
        return 'Out of Stock';
      default:
        return 'Unknown';
    }
  };

  const getColorStyle = (color: string) => {
    const colorMap: { [key: string]: string } = {
      natural: '#D2B48C',
      brown: '#8B4513',
      'dark brown': '#654321',
      mixed: '#6B7280',
      multicolor: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1)',
      'earth tones': '#CD853F',
    };
    return colorMap[color] || color;
  };

  return (
    <div
      className='group bg-white/90 dark:bg-navy-800/90 backdrop-blur-md rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer border border-gray-200/50 dark:border-navy-700/50 hover:border-gold-400/40 dark:hover:border-gold-400/40 transform hover:-translate-y-2'
      onClick={() => onQuickView?.(product)}
    >
      <div className='relative aspect-w-16 aspect-h-12 overflow-hidden'>
        <img
          src={product.image}
          alt={product.name}
          className='w-full h-64 object-cover group-hover:scale-110 transition-transform duration-700'
        />
        <div className='absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

        {/* Badges */}
        <div className='absolute top-3 left-3 flex flex-col gap-2'>
          <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getAvailabilityColor()}`}>
            {getAvailabilityText()}
          </span>
          {product.isNew && (
            <span className='px-3 py-1 rounded-full text-xs font-semibold bg-blue-600 text-white'>
              New
            </span>
          )}
          {product.discount && (
            <span className='px-3 py-1 rounded-full text-xs font-semibold bg-red-600 text-white'>
              -{product.discount}%
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className='absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
          <button className='p-2 bg-white/90 dark:bg-navy-800/90 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110'>
            <Heart className='h-4 w-4 text-navy-600 dark:text-gold-400 hover:text-red-500' />
          </button>
          {onQuickView && (
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onQuickView(product);
              }}
              className='p-2 bg-white/90 dark:bg-navy-800/90 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110'
            >
              <Eye className='h-4 w-4 text-navy-600 dark:text-gold-400' />
            </button>
          )}
        </div>
      </div>

      <div className='p-6'>
        <div className='mb-3'>
          <span className='text-xs text-gold-600 dark:text-gold-400 font-semibold uppercase tracking-wide'>
            {product.category.replace('-', ' ')}
          </span>
        </div>
        
        <h3 className='text-xl font-bold text-navy-900 dark:text-white mb-3 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors line-clamp-2'>
          {product.name}
        </h3>
        
        <p className='text-gray-600 dark:text-gray-300 mb-4 leading-relaxed text-sm line-clamp-2'>
          {product.description}
        </p>

        {/* Rating */}
        <div className='flex items-center space-x-1 mb-4'>
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < Math.floor(product.rating)
                  ? 'text-gold-400 fill-current'
                  : 'text-gray-300 dark:text-gray-600'
              }`}
            />
          ))}
          <span className='text-sm text-gray-600 dark:text-gray-400 ml-2 font-medium'>
            ({product.reviews})
          </span>
        </div>

        {/* Colors */}
        {product.colors.length > 0 && (
          <div className='flex items-center gap-2 mb-4'>
            <span className='text-sm text-gray-600 dark:text-gray-400 font-medium'>
              Colors:
            </span>
            <div className='flex gap-1'>
              {product.colors.slice(0, 4).map((color) => (
                <div
                  key={color}
                  className='w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 shadow-sm'
                  style={{
                    background: getColorStyle(color),
                  }}
                  title={color}
                />
              ))}
              {product.colors.length > 4 && (
                <span className='text-xs text-gray-500 dark:text-gray-400 font-medium'>
                  +{product.colors.length - 4}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Price and Add to Cart */}
        <div className='flex items-center justify-between pt-4 border-t border-gray-200 dark:border-navy-700'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2'>
              <span className='text-xl font-bold text-gold-600 dark:text-gold-400'>
                ${product.price.toFixed(2)}
              </span>
              {product.originalPrice && (
                <span className='text-sm text-gray-500 dark:text-gray-400 line-through'>
                  ${product.originalPrice.toFixed(2)}
                </span>
              )}
            </div>
          </div>
          
          <button
            onClick={handleAddToCart}
            disabled={product.availability === 'out-of-stock'}
            className='flex items-center space-x-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white px-4 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-semibold text-sm'
          >
            <ShoppingCart className='h-4 w-4' />
            <span>Add to Cart</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
