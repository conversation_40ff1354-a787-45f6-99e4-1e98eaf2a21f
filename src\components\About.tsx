import React from 'react';
import Button from './Button';

const About = () => {
  return (
    <section
      id='about'
      className='py-30 bg-gradient-to-br from-white via-amber-50/30 to-white
      dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative
      overflow-hidden'
    >
      {/* Elegant Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div className='absolute top-32 left-16 w-64 h-64 bg-gradient-to-r from-gold-300/10 to-gold-300/10 rounded-full blur-3xl animate-morph' />
        <div
          className='absolute bottom-32 right-16 w-80 h-80 bg-gradient-to-r from-navy-300/8 to-navy-400/8 rounded-full blur-3xl animate-morph'
          style={{ animationDelay: '4s' }}
        />
      </div>
      <div className='container mx-auto px-6 relative z-10'>
        <div className='max-w-6xl mx-auto'>
          {/* Section header */}
          <div className='text-center mb-24 mt-5'>
            {/* <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-navy-800/80 backdrop-blur-md rounded-full px-8 py-4 shadow-lg mb-8 border border-gold-200 dark:border-gold-700/30'>
              <span className='text-navy-800 dark:text-gold-300 font-semibold text-sm tracking-wide'>
                Our Journey
              </span>
            </div> */}
            <h2 className='font-serif text-4xl md:text-6xl font-light text-navy-800 dark:text-white mb-8 leading-tight'>
              From Refugee to
              <span className='block bg-gradient-to-r from-gold-500 via-gold-400 to-gold-500 dark:from-gold-400 dark:via-gold-300 dark:to-gold-400 bg-clip-text text-transparent'>
                Revolutionary
              </span>
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-8 rounded-full'></div>
            <p className='text-elegant text-lg md:text-xl text-navy-700 dark:text-white max-w-3xl mx-auto leading-relaxed'>
              A story of courage, resilience, and the transformative power of
              believing in human potential.
            </p>
          </div>

          {/* Timeline */}
          <div className='relative mb-24'>
            <div className='hidden md:block absolute left-1/2 transform -translate-x-px w-px h-full bg-gradient-to-b from-amber-200 via-amber-300 to-amber-200'></div>

            <div className='space-y-24'>
              {/* 1997 - The Journey Begins */}
              <div className='grid md:grid-cols-2 gap-16 items-center'>
                <div className='md:text-right'>
                  <div className='inline-block text-red-500 text-sm font-medium uppercase tracking-wider mb-4'>
                    1997 • Burundi
                  </div>
                  <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                    When Home Becomes Memory
                  </h3>
                  <p className='text-navy-700 dark:text-white leading-relaxed text-lg font-light mb-6'>
                    In Burundi's hills, Mme Cimpaye Modeste was an educator and
                    beacon of hope. When conflict shattered her world, she faced
                    an impossible choice.
                  </p>
                  <blockquote className='border-l-4 border-amber-200 pl-6 italic text-navy-700 dark:text-gold-300 font-light'>
                    "I carried nothing but my diploma and my dreams."
                  </blockquote>
                </div>
                <div className='relative'>
                  <div className='hidden md:block absolute -left-8 top-12 w-4 h-4 bg-red-400 rounded-full border-4 border-white shadow-lg'></div>
                  <div className='relative overflow-hidden rounded-2xl'>
                    <img
                      src='/assets/map.png'
                      alt='Journey from Burundi'
                      className='w-full h-80 object-cover'
                    />
                    <div className='absolute inset-0 bg-black/20'></div>
                  </div>
                </div>
              </div>

              {/* 1999 - The Awakening */}
              <div className='grid md:grid-cols-2 gap-16 items-center'>
                <div className='md:order-2'>
                  <div className='inline-block text-amber-500 text-sm font-medium uppercase tracking-wider mb-4'>
                    1999 • Kayole, Kenya
                  </div>
                  <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                    Where Despair Meets Determination
                  </h3>
                  <p className='text-navy-700 dark:text-white leading-relaxed text-lg font-light mb-6'>
                    In Kayole settlement, Modeste witnessed brilliant women
                    reduced by circumstance. Where others saw hopelessness, she
                    saw untapped potential.
                  </p>
                  <div className='bg-amber-50 border-l-4 border-amber-300 p-6 rounded-r'>
                    <p className='text-navy-700 dark:text-amber-700 italic font-light'>
                      "What if we didn't have to beg? What if we could create?"
                    </p>
                  </div>
                </div>
                <div className='relative md:order-1'>
                  <div className='hidden md:block absolute -right-8 top-12 w-4 h-4 bg-amber-400 rounded-full border-4 border-white shadow-lg'></div>
                  <div className='relative overflow-hidden rounded-2xl'>
                    <img
                      src='/assets/training1.jpg'
                      alt='Community awakening'
                      className='w-full h-80 object-cover'
                    />
                    <div className='absolute inset-0 bg-black/20'></div>
                  </div>
                </div>
              </div>

              {/* 1999 - WOPEDE Birth */}
              <div className='grid md:grid-cols-2 gap-16 items-center'>
                <div className='md:text-right'>
                  <div className='inline-block text-blue-500 text-sm font-medium uppercase tracking-wider mb-4'>
                    1999 • WOPEDE Founded
                  </div>
                  <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                    When Dreams Take Flight
                  </h3>
                  <p className='text-navy-700 dark:text-white  leading-relaxed text-lg font-light mb-6'>
                    WOPEDE was forged in necessity and tempered by hope. Over
                    100 women gathered, each bringing their skills and
                    determination to rewrite their destinies.
                  </p>
                  <div className='flex justify-end'>
                    <div className='grid grid-cols-2 gap-6'>
                      <div className='text-center'>
                        <div className='text-3xl font-serif font-light text-blue-500'>
                          100+
                        </div>
                        <div className='text-xs uppercase tracking-wider text-gray-500'>
                          Founding Members
                        </div>
                      </div>
                      <div className='text-center'>
                        <div className='text-3xl font-serif font-light text-blue-500'>
                          ∞
                        </div>
                        <div className='text-xs uppercase tracking-wider text-gray-500'>
                          Dreams Ignited
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='relative'>
                  <div className='hidden md:block absolute -left-8 top-12 w-4 h-4 bg-blue-400 rounded-full border-4 border-white shadow-lg'></div>
                  <div className='relative overflow-hidden rounded-2xl'>
                    <img
                      src='/assets/waving.jpg'
                      alt='WOPEDE foundation'
                      className='w-full h-80 object-cover'
                    />
                    <div className='absolute inset-0 bg-black/20'></div>
                  </div>
                </div>
              </div>

              {/* 2022 - Phoenix Rising */}
              <div className='grid md:grid-cols-2 gap-16 items-center'>
                <div className='md:order-2'>
                  <div className='inline-block text-green-500 text-sm font-medium uppercase tracking-wider mb-4'>
                    2022 • Reborn Stronger
                  </div>
                  <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                    The Phoenix Rises
                  </h3>
                  <p className='text-navy-700 dark:text-white  leading-relaxed text-lg font-light mb-6'>
                    After years of challenges, Modeste proved that true vision
                    never dies. WOPEDE was reborn, stronger and more determined
                    than ever.
                  </p>
                  <div className='space-y-2'>
                    <div className='flex items-center text-green-600'>
                      <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                      <span className='text-sm'>Survivors of violence</span>
                    </div>
                    <div className='flex items-center text-green-600'>
                      <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                      <span className='text-sm'>Refugee women & girls</span>
                    </div>
                    <div className='flex items-center text-green-600'>
                      <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                      <span className='text-sm'>Community partnerships</span>
                    </div>
                  </div>
                </div>
                <div className='relative md:order-1'>
                  <div className='hidden md:block absolute -right-8 top-12 w-4 h-4 bg-green-400 rounded-full border-4 border-white shadow-lg'></div>
                  <div className='relative overflow-hidden rounded-2xl'>
                    <img
                      src='/assets/stakeholders.jpg'
                      alt='Modern WOPEDE'
                      className='w-full h-80 object-cover'
                    />
                    <div className='absolute inset-0 bg-black/20'></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Founder spotlight */}
          <div className='bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-navy-800/90 dark:to-navy rounded-3xl shadow-xl overflow-hidden mb-20 border border-amber-100 dark:border-gold-700/20'>
            <div className='flex flex-col lg:flex-row'>
              <div className='lg:w-2/5'>
                <img
                  src='/assets/founder1.jpg'
                  alt='Cimpaye Modeste'
                  className='w-full h-96 lg:h-object-fit'
                />
              </div>
              <div className='lg:w-3/5 p-12'>
                <div className='mb-8'>
                  <h3 className='font-serif text-4xl font-light text-navy-800 dark:text-white mb-2'>
                    Cimpaye Modeste
                  </h3>
                  <p className='text-amber-400 font-medium mb-1'>
                    Founder & Visionary
                  </p>
                  <p className='text-gray-400 text-sm'>
                    Educator • Refugee • Revolutionary
                  </p>
                </div>

                <blockquote className='text-lg leading-relaxed italic text-navy-700 dark:text-white font-light border-l-4 border-amber-200 pl-6 mb-8'>
                  "Every woman carries within her the power to transform not
                  just her own life, but the lives of everyone around her."
                </blockquote>

                <div className='flex flex-wrap gap-3'>
                  <span className='px-4 py-2 bg-amber-50 text-amber-700 rounded-full text-sm'>
                    25+ Years Experience
                  </span>
                  <span className='px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm'>
                    Education Leader
                  </span>
                  <span className='px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm'>
                    Community Builder
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Mission, Vision, Values */}
          <div className='bg-gradient-to-br from-gray-50 to-white dark:from-navy-800/90 dark:to-navy  rounded-3xl p-12 shadow-xl'>
            <div className='text-center mb-16'>
              <h3 className='font-serif text-4xl font-light text-navy-800 dark:text-white mb-6'>
                Our Foundation
              </h3>
              <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 mx-auto mb-6 rounded-full'></div>
              <p className='text-xl text-navy-700 dark:text-white max-w-3xl mx-auto leading-relaxed font-light'>
                Built on three pillars that guide every decision, every program,
                and every life we touch.
              </p>
            </div>

            <div className='grid md:grid-cols-3 gap-8'>
              {/* Mission */}
              <div className='text-center p-8 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-amber-100 dark:border-gold-700/20'>
                {/* <div className='w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6'>
                  <span className='text-2xl'>🎯</span>
                </div> */}
                <h4 className='text-2xl font-bold text-blue-600 mb-4'>
                  Our Mission
                </h4>
                <p className='text-navy-700  leading-relaxed mb-6'>
                  WOPEDE CBO aims to empower women through practical skills,
                  emotional support, and resources that enable them to become
                  self-reliant and agents of peace in their communities. We
                  believe in the transformative power of women to create strong
                  families and harmonious societies.
                </p>
              </div>

              {/* Vision */}
              <div className='text-center p-8 bg-gradient-to-br from-amber-50 to-yellow-50  rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-amber-100 dark:border-gold-700/20'>
                {/* <div className='w-16 h-16 bg-amber-500 rounded-2xl flex items-center justify-center mx-auto mb-6'>
                  <span className='text-2xl'>🌟</span>
                </div> */}
                <h4 className='text-2xl font-bold text-amber-600 mb-4'>
                  Our Vision
                </h4>
                <p className='text-navy-700  leading-relaxed mb-6'>
                  WOPEDE envisions a world where women lead dignified lives,
                  support their families effectively, and act as ambassadors of
                  peace. We strive to break cycles of poverty and conflict by
                  fostering resilience, growth, and harmony.
                </p>
              </div>

              {/* Values */}
              <div className='text-center p-8 bg-gradient-to-br from-amber-50 to-yellow-50  rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-amber-100 dark:border-gold-700/20'>
                {/* <div className='w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6'>
                  <span className='text-2xl'>💝</span>
                </div> */}
                <h4 className='text-2xl font-bold text-green-600 mb-4'>
                  Our Values
                </h4>
                <div className='text-left space-y-3'>
                  <div className='flex items-start'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0'></div>
                    <div>
                      <span className='font-semibold text-green-800 '>
                        Empowerment:
                      </span>
                      <span className='text-navy-700  text-sm ml-1'>
                        Building confidence and independence in women.
                      </span>
                    </div>
                  </div>
                  <div className='flex items-start'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0'></div>
                    <div>
                      <span className='font-semibold text-green-800 '>
                        Resilience:
                      </span>
                      <span className='text-navy-700  text-sm ml-1'>
                        Inspiring strength in the face of adversity.
                      </span>
                    </div>
                  </div>
                  <div className='flex items-start'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0'></div>
                    <div>
                      <span className='font-semibold text-green-800 '>
                        Collaboration:
                      </span>
                      <span className='text-navy-700  text-sm ml-1'>
                        Partnering with communities and stakeholders.
                      </span>
                    </div>
                  </div>
                  <div className='flex items-start'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0'></div>
                    <div>
                      <span className='font-semibold text-green-800 '>
                        Sustainability:
                      </span>
                      <span className='text-navy-700  text-sm ml-1'>
                        Encouraging long-term growth and self-reliance.
                      </span>
                    </div>
                  </div>
                  <div className='flex items-start'>
                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0'></div>
                    <div>
                      <span className='font-semibold text-green-800 '>
                        Compassion:
                      </span>
                      <span className='text-navy-700 d text-sm ml-1'>
                        Providing a safe and supportive environment.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to action */}
            <div className='text-center mt-12'>
              <Button href='#contact'>
                <div className='flex items-center space-x-2'>
                  <span>Join Our Mission</span>
                  <span>→</span>
                </div>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
