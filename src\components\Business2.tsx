import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ShoppingBag,
  Users,
  Award,
  Globe,
  Handshake,
  Target,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
} from 'lucide-react';

const Business = () => {
  const [selectedProduct, setSelectedProduct] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(true);

  // Enhanced product data with detailed descriptions
  const products = [
    {
      name: 'Traditional Woven Baskets',
      description:
        'Beautifully handcrafted storage baskets made using traditional weaving techniques passed down through generations.',
      detailedDescription:
        'These stunning baskets are woven by skilled refugee women using natural materials and time-honored techniques. Perfect for home organization or as decorative pieces, each basket supports sustainable livelihoods.',
      image: '/assets/bus1.jpg',
      category: 'Traditional Crafts',
      price: '$25 - $45',
      features: [
        'Eco-friendly materials',
        'Handwoven',
        'Durable construction',
        'Cultural heritage',
      ],
    },
    {
      name: 'Colorful Textile Art',
      description:
        'Vibrant textile pieces showcasing the rich cultural traditions of our artisan community.',
      detailedDescription:
        'These beautiful textiles represent hours of meticulous work by talented women artisans. Using traditional patterns and modern color combinations, each piece brings warmth and culture to any space.',
      image: '/assets/bus2.jpg',
      category: 'Handwoven Items',
      price: '$35 - $75',
      features: [
        'Traditional patterns',
        'Vibrant colors',
        'Cultural significance',
        'Unique designs',
      ],
    },
    {
      name: 'Artisan Home Decor',
      description:
        'Elegant decorative pieces that blend traditional craftsmanship with contemporary design.',
      detailedDescription:
        'Transform your living space with these carefully crafted decorative items. Each piece reflects the artistic vision of refugee women who have turned their skills into sustainable businesses.',
      image: '/assets/bus3.jpg',
      category: 'Home Decor',
      price: '$20 - $60',
      features: [
        'Modern aesthetics',
        'Traditional techniques',
        'Versatile design',
        'Quality craftsmanship',
      ],
    },
    {
      name: 'Functional Storage Solutions',
      description:
        'Practical yet beautiful storage containers that organize your space while showcasing exceptional craftsmanship.',
      detailedDescription:
        'These storage solutions combine functionality with beauty, created by women who understand the importance of organized, beautiful living spaces.',
      image: '/assets/bus4.jpg',
      category: 'Storage Solutions',
      price: '$30 - $55',
      features: [
        'Multi-functional',
        'Space-saving',
        'Durable materials',
        'Aesthetic appeal',
      ],
    },
    {
      name: 'Cultural Heritage Pieces',
      description:
        'Authentic cultural artifacts and decorative items that preserve and celebrate rich traditions.',
      detailedDescription:
        'These pieces serve as bridges between cultures, sharing the beauty and significance of traditional art forms while providing economic opportunities for refugee women.',
      image: '/assets/bus5.jpg',
      category: 'Cultural Items',
      price: '$40 - $80',
      features: [
        'Cultural authenticity',
        'Historical significance',
        'Artistic value',
        'Community impact',
      ],
    },
    {
      name: 'Handmade Accessories',
      description:
        'Beautiful accessories and small decorative items perfect for gifts or personal use.',
      detailedDescription:
        'From jewelry to small decorative objects, these accessories showcase the fine motor skills and artistic sensibilities of our talented artisans.',
      image: '/assets/bus7.jpg',
      category: 'Artisan Products',
      price: '$15 - $35',
      features: [
        'Perfect for gifts',
        'Detailed craftsmanship',
        'Affordable luxury',
        'Personal touch',
      ],
    },
    {
      name: 'Decorative Art Pieces',
      description:
        'Stunning decorative pieces that serve as conversation starters and beautiful additions to any space.',
      detailedDescription:
        'These art pieces represent the creative expression of women who have overcome challenges to build new lives through their artistic talents.',
      image: '/assets/bus8.jpg',
      category: 'Decorative Pieces',
      price: '$50 - $120',
      features: [
        'Conversation pieces',
        'Artistic expression',
        'Premium quality',
        'Inspirational stories',
      ],
    },
    {
      name: 'Functional Art Collection',
      description:
        'Items that beautifully merge form and function, proving that everyday objects can be artistically inspiring.',
      detailedDescription:
        "This collection represents the pinnacle of our artisans' skills, creating pieces that serve daily needs while maintaining the highest standards of beauty.",
      image: '/assets/bus9.jpg',
      category: 'Functional Art',
      price: '$45 - $95',
      features: [
        'Form meets function',
        'Daily usability',
        'Artistic design',
        'Premium craftsmanship',
      ],
    },
  ];

  // Auto-slide functionality
  useEffect(() => {
    if (!isAutoSliding) return;

    const interval = setInterval(() => {
      setSelectedProduct((prev) => (prev + 1) % products.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoSliding, products.length]);

  const nextProduct = () => {
    setSelectedProduct((prev) => (prev + 1) % products.length);
  };

  const prevProduct = () => {
    setSelectedProduct(
      (prev) => (prev - 1 + products.length) % products.length
    );
  };

  const toggleAutoSlide = () => {
    setIsAutoSliding(!isAutoSliding);
  };

  const partners = [
    {
      name: 'Pamoja Trust',
      description:
        'Supporting community development and empowerment initiatives across Kenya.',
      focus: 'Community Development',
      icon: Users,
    },
    {
      name: 'Inkomoko',
      description:
        'Providing business training and financial services to entrepreneurs in East Africa.',
      focus: 'Business Training',
      icon: Target,
    },
    {
      name: 'Cohere',
      description:
        'Fostering social cohesion and integration in refugee and host communities.',
      focus: 'Social Cohesion',
      icon: Handshake,
    },
    {
      name: 'Refugee Point',
      description:
        'Connecting refugees with opportunities and resources for sustainable livelihoods.',
      focus: 'Refugee Support',
      icon: Globe,
    },
    {
      name: 'JRS (Jesuit Refugee Service)',
      description:
        'Serving, accompanying, and advocating for the rights of refugees and displaced persons.',
      focus: 'Refugee Services',
      icon: Award,
    },
  ];

  return (
    <section id='business' className='py-24 bg-white'>
      <div className='container mx-auto px-6'>
        <div className='max-w-7xl mx-auto'>
          {/* Enhanced section header with quote grid */}
          <div className='grid lg:grid-cols-3 gap-12 mb-20 items-start'>
            {/* Main intro content */}
            <div className='lg:col-span-2'>
              <div className='text-center lg:text-left'>
                <h2 className='text-5xl md:text-6xl font-bold text-gray-800 mb-8 tracking-tight'>
                  Our Business
                </h2>
                <div className='w-24 h-1 bg-gradient-to-r from-navy-800 to-navy-900 mx-auto lg:mx-0 mb-8 rounded-full'></div>
                <p className='text-xl text-gray-600 max-w-4xl mx-auto lg:mx-0 leading-relaxed font-light'>
                  WOPEDE creates beautiful handcrafted products while empowering
                  women with sustainable income opportunities. Every purchase
                  supports our mission of peace and development.
                </p>
              </div>
            </div>

            {/* Compact Inspirational Quote */}
            <div className='lg:col-span-1'>
              <div className='bg-gradient-to-br from-amber-50 to-yellow-50 rounded-2xl p-6 shadow-lg border border-amber-100 h-fit'>
                <div className='text-center'>
                  <div className='text-3xl text-amber-400 mb-3'>"</div>
                  <blockquote className='text-sm font-light text-gray-700 leading-relaxed mb-4 italic'>
                    In every thread we weave, we are crafting hope, building
                    futures, and painting tomorrow with colors of possibility.
                  </blockquote>
                  <div className='flex items-center justify-center space-x-3'>
                    <div className='w-10 h-10 rounded-full overflow-hidden shadow-md'>
                      <img
                        src='/assets/founder1.jpg'
                        alt='Cimpaye Modeste'
                        className='w-full h-full object-cover'
                      />
                    </div>
                    <div className='text-left'>
                      <p className='font-semibold text-gray-800 text-sm'>
                        Cimpaye Modeste
                      </p>
                      <p className='text-amber-600 font-medium text-xs'>
                        Founder, WOPEDE CBO
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Interactive Product Gallery */}
          <div className='mb-24'>
            <div className='flex items-center justify-center mb-16'>
              <ShoppingBag className='w-8 h-8 text-navy-800 mr-4' />
              <h3 className='text-4xl font-bold text-gray-800'>Our Products</h3>
            </div>

            {/* Main Product Display */}
            <div className='grid lg:grid-cols-3 gap-12 mb-16'>
              {/* Featured Product */}
              <div className='lg:col-span-2'>
                <div className='relative group'>
                  <div className='aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-gray-100 to-gray-200'>
                    <img
                      src={products[selectedProduct].image}
                      alt={products[selectedProduct].name}
                      className='w-full h-full object-cover transition-transform duration-700 group-hover:scale-105'
                    />
                    <div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent'></div>
                  </div>

                  {/* Navigation Arrows */}
                  <button
                    onClick={prevProduct}
                    className='absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-all duration-300 transform hover:scale-110'
                  >
                    <ChevronLeft className='w-6 h-6 text-gray-700' />
                  </button>

                  <button
                    onClick={nextProduct}
                    className='absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-all duration-300 transform hover:scale-110'
                  >
                    <ChevronRight className='w-6 h-6 text-gray-700' />
                  </button>

                  {/* Auto-slide Control */}
                  <button
                    onClick={toggleAutoSlide}
                    className='absolute bottom-4 right-4 w-10 h-10 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-all duration-300'
                  >
                    {isAutoSliding ? (
                      <Pause className='w-4 h-4' />
                    ) : (
                      <Play className='w-4 h-4' />
                    )}
                  </button>

                  {/* Product Counter */}
                  <div className='absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium'>
                    {selectedProduct + 1} / {products.length}
                  </div>
                </div>
              </div>

              {/* Product Details */}
              <div className='space-y-6'>
                <div>
                  <span className='text-base text-navy-800 font-semibold bg-navy-100 px-4 py-2 rounded-full inline-block mb-4'>
                    {products[selectedProduct].category}
                  </span>
                  <h3 className='text-3xl font-bold text-gray-800 mb-4'>
                    {products[selectedProduct].name}
                  </h3>
                  <p className='text-gray-600 leading-relaxed text-lg mb-6'>
                    {products[selectedProduct].detailedDescription}
                  </p>
                  <div className='text-2xl font-bold text-navy-800 mb-6'>
                    {products[selectedProduct].price}
                  </div>
                </div>

                {/* Features */}
                <div>
                  <h4 className='text-lg font-semibold text-gray-800 mb-4'>
                    Key Features
                  </h4>
                  <div className='space-y-2'>
                    {products[selectedProduct].features.map(
                      (feature, index) => (
                        <div key={index} className='flex items-center'>
                          <div className='w-2 h-2 bg-navy-800 rounded-full mr-3'></div>
                          <span className='text-gray-600'>{feature}</span>
                        </div>
                      )
                    )}
                  </div>
                </div>

                {/* Contact Button */}
                <a
                  href='#contact'
                  className='inline-block bg-gradient-to-r from-navy-800 to-navy-900 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-navy-900 hover:to-black transition-all duration-500 transform hover:scale-105 shadow-xl'
                >
                  Inquire About This Product
                </a>
              </div>
            </div>

            {/* Product Thumbnails */}
            <div className='grid grid-cols-4 md:grid-cols-8 gap-4 mb-8'>
              {products.map((product, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedProduct(index)}
                  className={`aspect-square rounded-xl overflow-hidden transition-all duration-300 transform hover:scale-105 ${
                    selectedProduct === index
                      ? 'ring-4 ring-navy-800 shadow-lg'
                      : 'hover:shadow-md'
                  }`}
                >
                  <img
                    src={product.image}
                    alt={product.name}
                    className='w-full h-full object-cover'
                  />
                </button>
              ))}
            </div>

            <div className='text-center'>
              <p className='text-xl text-gray-600 mb-8 font-light'>
                Interested in our products or bulk orders?
              </p>
              <a
                href='#contact'
                className='inline-block bg-gradient-to-r from-navy-800 to-navy-900 text-white px-12 py-4 rounded-2xl font-semibold text-lg hover:from-navy-900 hover:to-black transition-all duration-500 transform hover:scale-105 shadow-xl'
              >
                Contact Us for Orders
              </a>
            </div>
          </div>

          {/* Partners and Partnership Opportunities Section */}
          <div className='bg-gradient-to-br from-gray-50 to-white rounded-3xl p-12 shadow-2xl border border-gray-100'>
            <div className='text-center mb-16'>
              <h2 className='text-4xl font-bold text-gray-800 mb-4'>
                Partnerships & Collaboration
              </h2>
              <div className='w-24 h-1 bg-gradient-to-r from-navy-800 to-navy-900 mx-auto mb-6 rounded-full'></div>
              <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-light'>
                Building stronger communities through strategic partnerships and
                meaningful collaboration opportunities.
              </p>
            </div>

            <div className='grid lg:grid-cols-2 gap-16'>
              {/* Our Partners Section */}
              <div className='bg-gradient-to-br from-amber-500 to-amber-600 text-white rounded-3xl p-12 shadow-2xl border border-navy-700'>
                <div className='text-center mb-10'>
                  <div className='flex items-center justify-center mb-6'>
                    <Handshake className='w-8 h-8 text-navy-600 mr-8' />
                    <h3 className='text-4xl font-bold mb-5 tracking-tight'>
                      Our Partners
                    </h3>
                  </div>
                  <p className='text-lg text-white font-light leading-relaxed'>
                    Trusted organizations working alongside us to create lasting
                    change.
                  </p>
                </div>

                <div className='space-y-4'>
                  {partners.map((partner, index) => (
                    <div
                      key={index}
                      className='group bg-gray-50 hover:bg-white hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-navy-200 rounded-xl p-5'
                    >
                      <div className='flex items-start'>
                        <div className='w-10 h-10 bg-navy-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-navy-800 transition-colors duration-300 flex-shrink-0'>
                          <partner.icon className='w-5 h-5 text-navy-800 group-hover:text-white' />
                        </div>
                        <div className='flex-1'>
                          <div className='flex items-center mb-2'>
                            <h4 className='text-lg font-semibold text-navy-800 mr-3'>
                              {partner.name}
                            </h4>
                            <span className='text-xs text-yellow-600 font-semibold bg-yellow-100 px-2 py-1 rounded-full'>
                              {partner.focus}
                            </span>
                          </div>
                          <p className='text-gray-600 text-sm font-light leading-relaxed'>
                            {partner.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className='text-center mt-8'>
                  <p className='text-sm text-gray-100 mb-4'>
                    Interested in becoming a partner?
                  </p>
                  <a
                    href='#contact'
                    className='inline-block bg-navy-800 text-white px-6 py-3 rounded-xl font-semibold text-sm hover:bg-navy-900 transition-all duration-300 transform hover:scale-105'
                  >
                    Partner With Us
                  </a>
                </div>
              </div>

              {/* Partnership Opportunities Section */}
              <div className='bg-gradient-to-br from-navy-900 to-navy-800 text-white rounded-3xl p-12 shadow-2xl border border-navy-700'>
                <div className='text-center mb-10'>
                  <div className='flex items-center justify-center mb-6'>
                    <Target className='w-5 h-8 text-amber-600 mr-2' />
                    <h3 className='text-4xl font-bold mb-5 tracking-tight'>
                      Partnership Opportunities
                    </h3>
                  </div>
                  {/* <p className='text-xl mb-8 max-w-4xl mx-auto font-light leading-relaxed text-navy-100'> */}
                  <p className='text-lg text-navy-200 font-light leading-relaxed'>
                    Multiple ways to join our mission and make a meaningful
                    impact.
                  </p>
                </div>

                <div className='space-y-4'>
                  <div className='bg-white p-6 rounded-xl border border-yellow-200 hover:shadow-lg transition-all duration-300 hover:border-yellow-300'>
                    <div className='flex items-center mb-4'>
                      <div className='w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4'>
                        <Users className='w-5 h-5 text-blue-600' />
                      </div>
                      <h4 className='text-lg font-semibold text-gray-800'>
                        Volunteer Partnership
                      </h4>
                    </div>
                    <p className='text-gray-600 text-sm mb-4 leading-relaxed'>
                      Share your expertise and time to support our programs and
                      initiatives.
                    </p>
                    <a
                      href='#contact'
                      className='text-blue-600 font-medium text-sm hover:text-blue-700 transition-colors'
                    >
                      Learn More →
                    </a>
                  </div>

                  <div className='bg-white p-6 rounded-xl border border-yellow-200 hover:shadow-lg transition-all duration-300 hover:border-yellow-300'>
                    <div className='flex items-center mb-4'>
                      <div className='w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4'>
                        <Award className='w-5 h-5 text-green-600' />
                      </div>
                      <h4 className='text-lg font-semibold text-gray-800'>
                        Program Sponsorship
                      </h4>
                    </div>
                    <p className='text-gray-600 text-sm mb-4 leading-relaxed'>
                      Support specific training programs or sponsor individual
                      participants.
                    </p>
                    <a
                      href='#contact'
                      className='text-green-600 font-medium text-sm hover:text-green-700 transition-colors'
                    >
                      Explore Options →
                    </a>
                  </div>

                  <div className='bg-white p-6 rounded-xl border border-yellow-200 hover:shadow-lg transition-all duration-300 hover:border-yellow-300'>
                    <div className='flex items-center mb-4'>
                      <div className='w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4'>
                        <Globe className='w-5 h-5 text-purple-600' />
                      </div>
                      <h4 className='text-lg font-semibold text-gray-800'>
                        Strategic Collaboration
                      </h4>
                    </div>
                    <p className='text-gray-600 text-sm mb-4 leading-relaxed'>
                      Partner with us to expand reach and amplify impact in
                      communities.
                    </p>
                    <a
                      href='#contact'
                      className='text-purple-600 font-medium text-sm hover:text-purple-700 transition-colors'
                    >
                      Get Started →
                    </a>
                  </div>
                </div>

                <div className='text-center mt-8'>
                  <a
                    href='#contact'
                    className='inline-block bg-gradient-to-r from-yellow-400 to-yellow-500 text-navy-900 px-8 py-3 rounded-xl font-bold text-lg hover:from-yellow-300 hover:to-yellow-400 transition-all duration-500 transform hover:scale-105 shadow-lg border border-yellow-300'
                  >
                    Explore Partnership
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Business;
